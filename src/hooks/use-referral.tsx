'use client';

import * as React from "react"
import { useSearchParams, useRouter } from "next/navigation";
import {useCookies} from "react-cookie";

export function useReferral() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [cookies, setCookie] = useCookies(['referral']);

  React.useEffect(() => {
    const referral = searchParams.get('referral');
    if (referral) {
      localStorage.setItem('referral', referral);
      setCookie('referral', referral, {path: '/'});
      console.log("Saved referral id ", referral)

      // Remove the query parameter from URL without triggering navigation
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('referral');
      // Use Next.js router to update URL
      const newUrl = `${window.location.pathname}${newSearchParams.toString() ? '?' + newSearchParams.toString() : ''}`;
      router.replace(newUrl);
    }
  }, [searchParams, router, setCookie]);

  return cookies.referral;
}

