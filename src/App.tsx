import {Toaster} from "@/components/ui/toaster";
import {Toaster as Sonner} from "@/components/ui/sonner";
import {TooltipProvider} from "@/components/ui/tooltip";
import {QueryClient} from "@tanstack/react-query";
import {BrowserRouter, Routes, Route} from "react-router-dom";
import Index from './pages/Index'
import Studio from "./pages/Studio";
import Login from "./pages/Login";
import Profile from "./pages/Profile";
import Pricing from "./pages/Pricing";
import GenerationHistory from "./pages/GenerationHistory";
import NotFound from "./pages/NotFound";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsOfUse from "./pages/TermsOfUse";
import {PersistQueryClientProvider} from "@tanstack/react-query-persist-client";
import {createSyncStoragePersister} from "@tanstack/query-sync-storage-persister";
import {CookiesProvider} from "react-cookie";
import LandingSMM from "./pages/microLandings/SMM";
import LandingMarketplace from "./pages/microLandings/Marketplace";
import LandingInspiration from "./pages/microLandings/Inspiration";
import LandingLivingPhoto from "./pages/microLandings/LivePhoto";
import LandingInfluencer from "./pages/microLandings/Influencers";

const App = () => {
  const debug = import.meta.env.DEV;

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        gcTime: debug ? 50 : 1000 * 5, // 50 ms or 5 minutes
      },
    },
  })
  const persister = createSyncStoragePersister({
    storage: window.localStorage,
  })
  return (
      <PersistQueryClientProvider
        client={queryClient}
        persistOptions={{persister}}
      >
        <CookiesProvider>
          <TooltipProvider>
            <Toaster/>
            <Sonner/>
            <BrowserRouter>
              <Routes>
                <Route path="/" element={<Index/>}/>
                <Route path="/login" element={<Login/>}/>
                <Route path="/privacy-policy" element={<PrivacyPolicy/>}/>
                <Route path="/terms-of-use" element={<TermsOfUse/>}/>

                {/* MicroLandings */}
                <Route path="/smm" element={<LandingSMM/>}/>
                <Route path="/marketplace" element={<LandingMarketplace/>}/>
                <Route path="/inspiration" element={<LandingInspiration/>}/>
                <Route path="/living-photo" element={<LandingLivingPhoto/>}/>
                <Route path="/influencer" element={<LandingInfluencer/>}/>

                <Route element={<ProtectedRoute/>}>
                  <Route path="/studio" element={<Studio/>}/>
                  <Route path="/history" element={<GenerationHistory/>}/>
                  <Route path="/profile" element={<Profile/>}/>
                  <Route path="/pricing" element={<Pricing/>}/>
                </Route>

                <Route path="*" element={<NotFound/>}/>
              </Routes>
            </BrowserRouter>
          </TooltipProvider>
        </CookiesProvider>
      </PersistQueryClientProvider>
  );
};

export default App;
