import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
declare const ym: (id: number, methodName: string, ...args: any[]) => void;

export function safeYMGoal(targetName: string, value?: string) {
  try {
    if (!value) {
      ym(103438288, 'reachGoal', targetName);
    } else {
      ym(103438288, 'reachGoal', targetName, {order_price: value, currency: 'RUB'});
    }
    console.log('success calling ym goal', targetName, value);
  } catch (e) {
    console.error("Error while calling metrika goal - ",e)
  }
}
