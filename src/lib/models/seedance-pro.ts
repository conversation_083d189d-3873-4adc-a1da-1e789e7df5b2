import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const seedanceProConfig: ModelInputConfig = {
  modelId: 'seedance-pro',
  modelName: 'Seedance Pro',
  description: 'Профессиональная версия Seedance, которая предлагает поддержку генерации видео из текста и изображения для видео 5с или 10с, в разрешении 480p и 1080p',

  parameters: {
    // Input image for image-to-video
    image: {
      type: 'file',
      label: 'Входное изображение (опционально)',
      description: 'Входное изображение для начала генерации. Идеальные изображения 16:9 или 9:16 и 1280x720 или 720x1280, в зависимости от выбранного соотношения сторон.',
      accept: 'image/*',
      order: 1,
      group: 'advanced'
    },

    // Duration
    duration: {
      type: 'select',
      label: 'Длительность',
      description: 'Длительность видео в секундах',
      defaultValue: 5,
      options: [
        { value: 5, label: '5 секунд' },
        { value: 10, label: '10 секунд' }
      ],
      order: 2,
      group: 'advanced'
    },

    // Resolution
    resolution: {
      type: 'select',
      label: 'Разрешение',
      description: 'Разрешение видео',
      defaultValue: '1080p',
      options: [
        { value: '480p', label: '480p (Быстрее)' },
        { value: '1080p', label: '1080p (Высокое Качество)' }
      ],
      order: 3,
      group: 'advanced'
    },

    // Aspect ratio (ignored if image is used)
    aspect_ratio: {
      type: 'select',
      label: 'Соотношение сторон',
      description: 'Соотношение сторон видео',
      defaultValue: '16:9',
      options: [
        { value: '16:9', label: '16:9 (Альбомная)' },
        { value: '4:3', label: '4:3 (Стандартная)' },
        { value: '1:1', label: '1:1 (Квадрат)' },
        { value: '3:4', label: '3:4 (Портретная)' },
        { value: '9:16', label: '9:16 (Портретная)' },
        { value: '21:9', label: '21:9 (Ультраширокая)' },
        { value: '9:21', label: '9:21 (Высокая Портретная)' }
      ],
      order: 4,
      group: 'advanced',
      showWhen: {
        field: 'image',
        value: false // Show when no image is uploaded
      }
    },

    // Frame rate (fixed at 24)
    fps: {
      type: 'number',
      label: 'FPS',
      description: 'Кадров в секунду выходного видео',
      defaultValue: 24,
      min: 24,
      max: 24,
      order: 5,
      group: 'advanced'
    },

    // Camera settings
    camera_fixed: {
      type: 'boolean',
      label: 'Фиксированная камера',
      description: 'Зафиксировать ли позицию камеры',
      defaultValue: false,
      order: 6,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Случайное число для воспроизводимой генерации. Оставьте пустым для случайного значения.',
      order: 7,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: 'Расширенные настройки'
    }
  },

  metadata: {
    type: 'video',
    maxOutputs: 1,
    supportedFormats: ['mp4'],
    maxResolution: '1080p',
    estimatedTime: '1-3 minutes'
  }
};
