import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const seedream3Config: ModelInputConfig = {
  modelId: 'seedream-3',
  modelName: 'Seedream 3',
  description: 'Модель преобразования текста в изображение с поддержкой генерации изображений с высоким разрешением (2K)',

  parameters: {
    // Aspect ratio with Seedream-3 specific options
    aspect_ratio: {
      type: 'select',
      label: 'Соотношение сторон',
      description: 'Соотношение сторон сгенерированного изображения',
      defaultValue: '16:9',
      options: [
        { value: '1:1', label: '1:1 (Квадрат)' },
        { value: '3:4', label: '3:4 (Портрет)' },
        { value: '4:3', label: '4:3 (Стандартный)' },
        { value: '16:9', label: '16:9 (Пейзаж)' },
        { value: '9:16', label: '9:16 (Мобильный портрет)' },
        { value: '2:3', label: '2:3 (Фотопортрет)' },
        { value: '3:2', label: '3:2 (Фото)' },
        { value: '21:9', label: '21:9 (Сверхширокий)' },
        { value: 'custom', label: 'Пользовательский (указать ширину/высоту)' }
      ],
      order: 1,
      group: 'advanced'
    },

    // Size setting (unique to Seedream-3)
    size: {
      type: 'select',
      label: 'Размер изображения',
      description: 'Большие изображения будут иметь самую длинную сторону 2048 пикселей. Короткие изображения будут иметь самую короткую сторону 512 пикселей. Обычные изображения всегда будут иметь 1 мегапиксель. Игнорируется, если соотношение сторон пользовательское.',
      defaultValue: 'regular',
      options: [
        { value: 'small', label: 'Маленький (512px самая короткая)' },
        { value: 'regular', label: 'Обычный (1 мегапиксель)' },
        { value: 'big', label: 'Большой (2048px самая длинная)' }
      ],
      order: 2,
      group: 'advanced',
      showWhen: {
        field: 'aspect_ratio',
        value: '!custom' // Show when aspect_ratio is NOT custom
      }
    },

    // Custom width (only when aspect_ratio is custom)
    width: {
      type: 'number',
      label: 'Ширина',
      description: 'Ширина изображения в пикселях',
      defaultValue: 2048,
      min: 512,
      max: 2048,
      step: 64,
      order: 3,
      group: 'advanced',
      showWhen: {
        field: 'aspect_ratio',
        value: 'custom'
      }
    },

    // Custom height (only when aspect_ratio is custom)
    height: {
      type: 'number',
      label: 'Высота',
      description: 'Высота изображения в пикселях',
      defaultValue: 2048,
      min: 512,
      max: 2048,
      step: 64,
      order: 4,
      group: 'advanced',
      showWhen: {
        field: 'aspect_ratio',
        value: 'custom'
      }
    },

    // Guidance scale
    guidance_scale: {
      type: 'slider',
      label: 'Масштаб руководства',
      description: 'Соответствие запросу. Более высокие значения = более буквальная интерпретация запроса.',
      defaultValue: 2.5,
      min: 1,
      max: 10,
      step: 0.5,
      order: 5,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Случайное начальное значение. Устанавливается для воспроизводимой генерации',
      order: 6,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: 'Расширенные настройки'
    }
  },

  metadata: {
    type: 'image',
    maxOutputs: 1,
    supportedFormats: ['jpg'],
    maxResolution: '2K',
    estimatedTime: '5-15 seconds'
  }
};
