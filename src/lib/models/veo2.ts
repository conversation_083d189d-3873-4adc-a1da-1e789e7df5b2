import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const veo2Config: ModelInputConfig = {
  modelId: 'veo2',
  modelName: 'Veo 2',
  description: 'State of the art video generation model. Veo 2 can faithfully follow simple and complex instructions, and convincingly simulates real-world physics as well as a wide range of visual styles.',

  parameters: {
    // Input image for image-to-video
    image: {
      type: 'file',
      label: 'Input Image (Optional)',
      description: 'Input image to start generating from. Ideal images are 16:9 or 9:16 and 1280x720 or 720x1280, depending on the aspect ratio you choose.',
      accept: 'image/*',
      order: 1,
      group: 'advanced'
    },

    // Aspect ratio
    aspect_ratio: {
      type: 'select',
      label: 'Aspect Ratio',
      description: 'Video aspect ratio',
      defaultValue: '16:9',
      options: [
        { value: '16:9', label: '16:9 (Landscape)' },
        { value: '9:16', label: '9:16 (Portrait)' }
      ],
      order: 2,
      group: 'advanced'
    },

    // Duration
    duration: {
      type: 'select',
      label: 'Duration',
      description: 'Video duration in seconds',
      defaultValue: 5,
      options: [
        { value: 5, label: '5 seconds' },
        { value: 6, label: '6 seconds' },
        { value: 7, label: '7 seconds' },
        { value: 8, label: '8 seconds' }
      ],
      order: 3,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Random seed. Omit for random generations',
      order: 4,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: 'Расширенные настройки'
    }
  },

  metadata: {
    type: 'video',
    maxOutputs: 1,
    supportedFormats: ['mp4'],
    maxResolution: '4K',
    estimatedTime: '1-3 minutes'
  }
};
