import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const veo2Config: ModelInputConfig = {
  modelId: 'veo2',
  modelName: 'Veo 2',
  description: 'Современная модель генерации видео. Veo 2 может точно следовать простым и сложным инструкциям, убедительно имитируя физику реального мира и широкий спектр визуальных стилей.',

  parameters: {
    // Input image for image-to-video
    image: {
      type: 'file',
      label: 'Входное изображение (опционально)',
      description: 'Входное изображение для начала генерации. Идеальные изображения 16:9 или 9:16 и 1280x720 или 720x1280, в зависимости от выбранного соотношения сторон.',
      accept: 'image/*',
      order: 1,
      group: 'advanced'
    },

    // Aspect ratio
    aspect_ratio: {
      type: 'select',
      label: 'Соотношение сторон',
      description: 'Соотношение сторон видео',
      defaultValue: '16:9',
      options: [
        { value: '16:9', label: '16:9 (Альбомная)' },
        { value: '9:16', label: '9:16 (Портретная)' }
      ],
      order: 2,
      group: 'advanced'
    },

    // Duration
    duration: {
      type: 'select',
      label: 'Длительность',
      description: 'Длительность видео в секундах',
      defaultValue: 5,
      options: [
        { value: 5, label: '5 секунд' },
        { value: 6, label: '6 секунд' },
        { value: 7, label: '7 секунд' },
        { value: 8, label: '8 секунд' }
      ],
      order: 3,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Случайное число для воспроизводимой генерации. Оставьте пустым для случайного значения.',
      order: 4,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: 'Расширенные настройки'
    }
  },

  metadata: {
    type: 'video',
    maxOutputs: 1,
    supportedFormats: ['mp4'],
    maxResolution: '4K',
    estimatedTime: '1-3 minutes'
  }
};
