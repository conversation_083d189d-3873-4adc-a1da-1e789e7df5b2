import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const imagen4UltraConfig: ModelInputConfig = {
  modelId: 'imagen-4-ultra',
  modelName: 'Imagen 4 Ultra',
  description: 'Используйте эту ультра-версию Imagen 4, когда качество важнее скорости и стоимости',

  parameters: {
    // Aspect ratio with Imagen-4-Ultra specific options
    aspect_ratio: {
      type: 'select',
      label: 'Соотношение сторон',
      description: 'Соотношение сторон сгенерированного изображения',
      defaultValue: '1:1',
      options: [
        { value: '1:1', label: '1:1 (Квадрат)' },
        { value: '9:16', label: '9:16 (М<PERSON><PERSON><PERSON><PERSON><PERSON>ный портрет)' },
        { value: '16:9', label: '16:9 (Пейзаж)' },
        { value: '3:4', label: '3:4 (Портрет)' },
        { value: '4:3', label: '4:3 (Стандартный)' }
      ],
      order: 1,
      group: 'advanced'
    },

    // Safety filter level (unique to Imagen-4-Ultra)
    safety_filter_level: {
      type: 'select',
      label: 'Уровень фильтра безопасности',
      description: 'Уровень фильтрации безопасности контента. "block_low_and_above" является самым строгим, "block_medium_and_above" блокирует некоторые подсказки, "block_only_high" является наиболее разрешительным, но некоторые подсказки все равно будут заблокированы',
      defaultValue: 'block_only_high',
      options: [
        {
          value: 'block_low_and_above',
          label: 'Строгий (блокировать низкий и выше)',
          description: 'Наиболее ограничительная фильтрация'
        },
        {
          value: 'block_medium_and_above',
          label: 'Умеренный (блокировать средний и выше)',
          description: 'Сбалансированная фильтрация'
        },
        {
          value: 'block_only_high',
          label: 'Разрешительный (блокировать только высокий)',
          description: 'Наименее ограничительная фильтрация'
        }
      ],
      order: 2,
      group: 'advanced'
    },

    // Output format
    output_format: {
      type: 'select',
      label: 'Формат вывода',
      description: 'Формат выходных изображений',
      defaultValue: 'jpg',
      options: [
        { value: 'jpg', label: 'JPEG' },
        { value: 'png', label: 'PNG' }
      ],
      order: 3,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: 'Расширенные настройки'
    }
  },

  metadata: {
    type: 'image',
    maxOutputs: 1,
    supportedFormats: ['jpg', 'png'],
    maxResolution: '2K',
    estimatedTime: '15-45 seconds'
  }
};
