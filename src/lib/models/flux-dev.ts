import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const fluxDevConfig: ModelInputConfig = {
  modelId: 'flux-dev',
  modelName: 'FLUX Dev',
  description: 'Трансформер с 12 миллиардами параметров, способный генерировать изображения из текстовых описаний',

  parameters: {
    // Aspect ratio with FLUX-specific options
    aspect_ratio: {
      type: 'select',
      label: 'Соотношение сторон',
      description: 'Соотношение сторон сгенерированного изображения',
      defaultValue: '1:1',
      options: [
        { value: '1:1', label: '1:1 (Квадрат)' },
        { value: '16:9', label: '16:9 (Пе<PERSON>з<PERSON>ж)' },
        { value: '21:9', label: '21:9 (Сверхширокий)' },
        { value: '3:2', label: '3:2 (Фото)' },
        { value: '2:3', label: '2:3 (Фотопортрет)' },
        { value: '4:5', label: '4:5 (Портрет)' },
        { value: '5:4', label: '5:4 (Пейзаж)' },
        { value: '3:4', label: '3:4 (Портрет)' },
        { value: '4:3', label: '4:3 (Стандартный)' },
        { value: '9:16', label: '9:16 (Мобильный портрет)' },
        { value: '9:21', label: '9:21 (Высокий портрет)' }
      ],
      order: 1,
      group: 'advanced'
    },

    // Image input for img2img mode
    image: {
      type: 'file',
      label: 'Входное изображение (необязательно)',
      description: 'Входное изображение для режима преобразования изображения в изображение. Соотношение сторон выходного изображения будет соответствовать этому изображению',
      accept: 'image/*',
      order: 2,
      group: 'advanced'
    },

    // Prompt strength for img2img
    prompt_strength: {
      type: 'slider',
      label: 'Сила запроса',
      description: 'Сила запроса при использовании преобразования изображения в изображение. 1.0 соответствует полному разрушению информации в изображении',
      defaultValue: 0.8,
      min: 0,
      max: 1,
      step: 0.1,
      order: 3,
      group: 'advanced',
      showWhen: {
        field: 'image',
        value: true // Show when image is uploaded
      }
    },

    // Number of outputs
    num_outputs: {
      type: 'select',
      label: 'Количество изображений',
      description: 'Количество изображений для генерации',
      defaultValue: 1,
      options: [
        { value: 1, label: '1 изображение' },
        { value: 2, label: '2 изображения' },
        { value: 4, label: '4 изображения' }
      ],
      order: 4,
      group: 'advanced'
    },

    // Quality and performance settings
    num_inference_steps: {
      type: 'slider',
      label: 'Шаги инференции',
      description: 'Количество шагов шумоподавления. Рекомендуемый диапазон 28-50. Меньшие значения быстрее, но дают более низкое качество.',
      defaultValue: 28,
      min: 1,
      max: 50,
      step: 1,
      order: 5,
      group: 'advanced'
    },

    guidance: {
      type: 'slider',
      label: 'Руководство',
      description: 'Руководство по сгенерированному изображению. Меньшие значения могут давать более реалистичные изображения. Хорошие значения: 2, 2.5, 3, 3.5',
      defaultValue: 3,
      min: 0,
      max: 10,
      step: 0.5,
      order: 6,
      group: 'advanced'
    },

    go_fast: {
      type: 'boolean',
      label: 'Быстрый режим',
      description: 'Создавайте изображение быстрее с дополнительными оптимизациями',
      defaultValue: true,
      order: 7,
      group: 'advanced'
    },

    megapixels: {
      type: 'select',
      label: 'Мегапиксели',
      description: 'Приблизительное количество мегапикселей для сгенерированного изображения',
      defaultValue: '1',
      options: [
        { value: '0.25', label: '0.25 МП (Быстрее)' },
        { value: '1', label: '1 МП (Стандартно)' }
      ],
      order: 8,
      group: 'advanced'
    },

    // Output settings
    output_format: {
      type: 'select',
      label: 'Формат вывода',
      description: 'Формат выходных изображений',
      defaultValue: 'webp',
      options: [
        { value: 'webp', label: 'WebP (Рекомендуется)' },
        { value: 'jpg', label: 'JPEG' },
        { value: 'png', label: 'PNG' }
      ],
      order: 9,
      group: 'advanced'
    },

    output_quality: {
      type: 'slider',
      label: 'Качество вывода',
      description: 'Качество при сохранении выходных изображений, от 0 до 100. 100 - лучшее качество. Неактуально для PNG-выводов',
      defaultValue: 80,
      min: 0,
      max: 100,
      step: 5,
      order: 10,
      group: 'advanced'
    },

    disable_safety_checker: {
      type: 'boolean',
      label: 'Отключить проверку безопасности',
      description: 'Отключить проверку безопасности для сгенерированных изображений',
      defaultValue: false,
      order: 11,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Случайное начальное значение. Устанавливается для воспроизводимой генерации',
      order: 12,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: 'Расширенные настройки'
    }
  },

  metadata: {
    type: 'image',
    maxOutputs: 4,
    supportedFormats: ['webp', 'jpg', 'png'],
    maxResolution: '2K',
    estimatedTime: '10-30 seconds'
  }
};
