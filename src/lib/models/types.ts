// Types for model input configurations and UI component mappings


export type UIComponentType =
  | 'text'
  | 'textarea'
  | 'number'
  | 'select'
  | 'boolean'
  | 'file'
  | 'slider';

export interface UIComponentConfig {
  type: UIComponentType;
  label: string;
  description?: string;
  required?: boolean;
  defaultValue?: any;
  placeholder?: string;

  // For number inputs and sliders
  min?: number;
  max?: number;
  step?: number;

  // For select inputs
  options?: Array<{
    value: string | number | boolean;
    label: string;
    description?: string;
  }>;

  // For file inputs
  accept?: string;

  // UI ordering and grouping
  order?: number;
  group?: string;

  // Conditional display
  showWhen?: {
    field: string;
    value: any;
  };
}

export interface ParameterGroup {
  name: string;
  label: string;
  description?: string;
  order?: number;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export interface ModelInputConfig {
  modelId: string;
  modelName: string;
  description: string;

  // Parameter definitions
  parameters: Record<string, UIComponentConfig>;

  // Parameter grouping
  groups?: Record<string, ParameterGroup>;

  // Model-specific metadata
  metadata?: {
    type: 'image' | 'video';
    maxOutputs?: number;
    supportedFormats?: string[];
    maxResolution?: string;
    estimatedTime?: string;
  };
}

// Common parameter configurations that can be reused across models
export const CommonParameters = {
  aspectRatio: {
    type: 'select' as const,
    label: 'Aspect Ratio',
    description: 'The aspect ratio of the generated content',
    defaultValue: '1:1',
    options: [
      { value: '1:1', label: '1:1 (Square)' },
      { value: '16:9', label: '16:9 (Landscape)' },
      { value: '9:16', label: '9:16 (Portrait)' },
      { value: '4:3', label: '4:3 (Standard)' },
      { value: '3:4', label: '3:4 (Portrait)' },
      { value: '21:9', label: '21:9 (Ultrawide)' },
      { value: '3:2', label: '3:2 (Photo)' },
      { value: '2:3', label: '2:3 (Photo Portrait)' }
    ],
    order: 1,
    group: 'basic'
  },

  // Video-specific aspect ratios
  videoAspectRatio: {
    type: 'select' as const,
    label: 'Aspect Ratio',
    description: 'Video aspect ratio',
    defaultValue: '16:9',
    options: [
      { value: '16:9', label: '16:9 (Landscape)' },
      { value: '9:16', label: '9:16 (Portrait)' },
      { value: '4:3', label: '4:3 (Standard)' },
      { value: '3:4', label: '3:4 (Portrait)' },
      { value: '1:1', label: '1:1 (Square)' },
      { value: '21:9', label: '21:9 (Ultrawide)' },
      { value: '9:21', label: '9:21 (Tall Portrait)' }
    ],
    order: 1,
    group: 'basic'
  },

  seed: {
    type: 'number' as const,
    label: 'Сид',
    description: 'Random seed for reproducible generation. Leave empty for random.',
    min: 0,
    max: 2147483647,
    order: 100,
    group: 'advanced'
  },

  numOutputs: {
    type: 'select' as const,
    label: 'Number of Images',
    description: 'How many images to generate',
    defaultValue: 1,
    options: [
      { value: 1, label: '1 image' },
      { value: 2, label: '2 images' },
      { value: 4, label: '4 images' }
    ],
    order: 2,
    group: 'basic'
  },

  // Common video parameters
  duration: {
    type: 'select' as const,
    label: 'Duration',
    description: 'Video duration in seconds',
    defaultValue: 5,
    options: [
      { value: 5, label: '5 seconds' },
      { value: 6, label: '6 seconds' },
      { value: 7, label: '7 seconds' },
      { value: 8, label: '8 seconds' },
      { value: 10, label: '10 seconds' }
    ],
    order: 2,
    group: 'basic'
  },

  fps: {
    type: 'number' as const,
    label: 'FPS',
    description: 'Frames per second',
    defaultValue: 24,
    min: 1,
    max: 60,
    order: 3,
    group: 'basic'
  },

  enhancePrompt: {
    type: 'boolean' as const,
    label: 'Enhance Prompt',
    description: 'Use AI to enhance your prompt for better results',
    defaultValue: true,
    order: 1,
    group: 'quality'
  },

  negativePrompt: {
    type: 'textarea' as const,
    label: 'Negative Prompt',
    description: 'Describe what you want to avoid in the generated video',
    placeholder: 'e.g., blurry, low quality, distorted...',
    order: 2,
    group: 'quality'
  }
} as const;

// Common parameter groups
export const CommonGroups = {
  basic: {
    name: 'basic',
    label: 'Basic Settings',
    description: 'Essential parameters for generation',
    order: 0,
    defaultExpanded: true
  },

  quality: {
    name: 'quality',
    label: 'Quality & Performance',
    description: 'Settings that affect output quality and generation speed',
    order: 1,
    defaultExpanded: true
  },

  advanced: {
    name: 'advanced',
    label: 'Расширенные настройки',
    description: 'Дополнительные возможности настройки генерации',
    order: 2,
    collapsible: true,
    defaultExpanded: false
  },

  modelSpecific: {
    name: 'modelSpecific',
    label: 'Model-Specific Options',
    description: 'Parameters unique to this model',
    order: 3,
    collapsible: true,
    defaultExpanded: false
  }
} as const;
