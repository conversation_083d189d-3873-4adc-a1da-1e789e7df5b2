// Types for model input configurations and UI component mappings


export type UIComponentType =
  | 'text'
  | 'textarea'
  | 'number'
  | 'select'
  | 'boolean'
  | 'file'
  | 'slider';

export interface UIComponentConfig {
  type: UIComponentType;
  label: string;
  description?: string;
  required?: boolean;
  defaultValue?: any;
  placeholder?: string;

  // For number inputs and sliders
  min?: number;
  max?: number;
  step?: number;

  // For select inputs
  options?: Array<{
    value: string | number | boolean;
    label: string;
    description?: string;
  }>;

  // For file inputs
  accept?: string;

  // UI ordering and grouping
  order?: number;
  group?: string;

  // Conditional display
  showWhen?: {
    field: string;
    value: any;
  };
}

export interface ParameterGroup {
  name: string;
  label: string;
  description?: string;
  order?: number;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export interface ModelInputConfig {
  modelId: string;
  modelName: string;
  description: string;

  // Parameter definitions
  parameters: Record<string, UIComponentConfig>;

  // Parameter grouping
  groups?: Record<string, ParameterGroup>;

  // Model-specific metadata
  metadata?: {
    type: 'image' | 'video';
    maxOutputs?: number;
    supportedFormats?: string[];
    maxResolution?: string;
    estimatedTime?: string;
  };
}

// Common parameter configurations that can be reused across models
export const CommonParameters = {
  aspectRatio: {
    type: 'select' as const,
    label: 'Соотношение сторон',
    description: 'Соотношение сторон сгенерированного изображения',
    defaultValue: '1:1',
    options: [
      { value: '1:1', label: '1:1 (Квадрат)' },
      { value: '16:9', label: '16:9 (Пейзаж)' },
      { value: '9:16', label: '9:16 (Мобильный портрет)' },
      { value: '4:3', label: '4:3 (Стандартный)' },
      { value: '3:4', label: '3:4 (Портрет)' },
      { value: '21:9', label: '21:9 (Сверхширокий)' },
      { value: '3:2', label: '3:2 (Фото)' },
      { value: '2:3', label: '2:3 (Фотопортрет)' }
    ],
    order: 1,
    group: 'basic'
  },

  // Video-specific aspect ratios
  videoAspectRatio: {
    type: 'select' as const,
    label: 'Соотношение сторон',
    description: 'Соотношение сторон видео',
    defaultValue: '16:9',
    options: [
      { value: '16:9', label: '16:9 (Пейзаж)' },
      { value: '9:16', label: '9:16 (Портрет)' },
      { value: '4:3', label: '4:3 (Стандартный)' },
      { value: '3:4', label: '3:4 (Портрет)' },
      { value: '1:1', label: '1:1 (Квадрат)' },
      { value: '21:9', label: '21:9 (Сверхширокий)' },
      { value: '9:21', label: '9:21 (Высокий портрет)' }
    ],
    order: 1,
    group: 'basic'
  },

  seed: {
    type: 'number' as const,
    label: 'Сид',
    description: 'Случайное начальное значение. Устанавливается для воспроизводимой генерации',
    min: 0,
    max: 2147483647,
    order: 100,
    group: 'advanced'
  },

  numOutputs: {
    type: 'select' as const,
    label: 'Количество изображений',
    description: 'Количество изображений для генерации',
    defaultValue: 1,
    options: [
      { value: 1, label: '1 изображение' },
      { value: 2, label: '2 изображения' },
      { value: 4, label: '4 изображения' }
    ],
    order: 2,
    group: 'basic'
  },

  // Common video parameters
  duration: {
    type: 'select' as const,
    label: 'Длительность',
    description: 'Длительность видео в секундах',
    defaultValue: 5,
    options: [
      { value: 5, label: '5 секунд' },
      { value: 6, label: '6 секунд' },
      { value: 7, label: '7 секунд' },
      { value: 8, label: '8 секунд' },
      { value: 10, label: '10 секунд' }
    ],
    order: 2,
    group: 'basic'
  },

  fps: {
    type: 'number' as const,
    label: 'FPS',
    description: 'Кадров в секунду',
    defaultValue: 24,
    min: 1,
    max: 60,
    order: 3,
    group: 'basic'
  },

  enhancePrompt: {
    type: 'boolean' as const,
    label: 'Улучшить запрос',
    description: 'Использовать ИИ для улучшения вашего запроса, чтобы получить лучший результат',
    defaultValue: true,
    order: 1,
    group: 'quality'
  },

  negativePrompt: {
    type: 'textarea' as const,
    label: 'Негативный запрос',
    description: 'Описание того, чего следует избегать',
    placeholder: 'например, размытый, низкое качество, искаженный...',
    order: 2,
    group: 'quality'
  }
} as const;

// Common parameter groups
export const CommonGroups = {
  basic: {
    name: 'basic',
    label: 'Основные настройки',
    description: 'Основные параметры для генерации',
    order: 0,
    defaultExpanded: true
  },

  quality: {
    name: 'quality',
    label: 'Качество и производительность',
    description: 'Настройки, влияющие на качество вывода и скорость генерации',
    order: 1,
    defaultExpanded: true
  },

  advanced: {
    name: 'advanced',
    label: 'Расширенные настройки',
    description: 'Дополнительные возможности настройки генерации',
    order: 2,
    collapsible: true,
    defaultExpanded: false
  },

  modelSpecific: {
    name: 'modelSpecific',
    label: 'Специфичные для модели опции',
    description: 'Параметры, уникальные для этой модели',
    order: 3,
    collapsible: true,
    defaultExpanded: false
  }
} as const;
