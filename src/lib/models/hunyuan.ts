import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const hunyuanConfig: ModelInputConfig = {
  modelId: 'hunyuan',
  modelName: 'Hunyuan Video',
  description: 'A state-of-the-art text-to-video generation model capable of creating high-quality videos with realistic motion from text descriptions',

  parameters: {
    // Video dimensions
    width: {
      type: 'number',
      label: 'Width',
      description: 'Width of the video in pixels (must be divisible by 16)',
      defaultValue: 864,
      min: 16,
      max: 1280,
      step: 16,
      order: 1,
      group: 'advanced'
    },

    height: {
      type: 'number',
      label: 'Height',
      description: 'Height of the video in pixels (must be divisible by 16)',
      defaultValue: 480,
      min: 16,
      max: 1280,
      step: 16,
      order: 2,
      group: 'advanced'
    },

    // Video length
    video_length: {
      type: 'select',
      label: 'Video Length',
      description: 'Number of frames to generate (must be 4k+1, ex: 49 or 129)',
      defaultValue: 129,
      options: [
        { value: 49, label: '49 frames (~2 seconds)' },
        { value: 129, label: '129 frames (~5 seconds)' }
      ],
      order: 3,
      group: 'advanced'
    },

    // Quality settings
    infer_steps: {
      type: 'slider',
      label: 'Inference Steps',
      description: 'Number of denoising steps. Higher values improve quality but take longer.',
      defaultValue: 50,
      min: 1,
      max: 100,
      step: 1,
      order: 4,
      group: 'advanced'
    },

    embedded_guidance_scale: {
      type: 'slider',
      label: 'Guidance Scale',
      description: 'Controls how closely the model follows the prompt. Higher values = more adherence.',
      defaultValue: 6,
      min: 1,
      max: 10,
      step: 0.5,
      order: 5,
      group: 'advanced'
    },

    // Frame rate
    fps: {
      type: 'number',
      label: 'FPS',
      description: 'Frames per second of the output video',
      defaultValue: 24,
      min: 1,
      max: 60,
      order: 6,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Random seed for reproducible generation. Leave empty for random.',
      order: 7,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: 'Расширенные настройки'
    }
  },

  metadata: {
    type: 'video',
    maxOutputs: 1,
    supportedFormats: ['mp4'],
    maxResolution: '1280x1280',
    estimatedTime: '2-5 minutes'
  }
};
