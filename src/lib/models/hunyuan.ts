import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const hunyuanConfig: ModelInputConfig = {
  modelId: 'hunyuan',
  modelName: 'Hunyuan Video',
  description: 'Современная модель генерации видео из текста, способная создавать высококачественные видео с реалистичным движением из текстовых описаний',

  parameters: {
    // Video dimensions
    width: {
      type: 'number',
      label: 'Ширина',
      description: 'Ширина видео в пикселях (должна быть кратна 16)',
      defaultValue: 864,
      min: 16,
      max: 1280,
      step: 16,
      order: 1,
      group: 'advanced'
    },

    height: {
      type: 'number',
      label: 'Высота',
      description: 'Высота видео в пикселях (должна быть кратна 16)',
      defaultValue: 480,
      min: 16,
      max: 1280,
      step: 16,
      order: 2,
      group: 'advanced'
    },

    // Video length
    video_length: {
      type: 'select',
      label: 'Длина видео',
      description: 'Количество кадров для генерации (должно быть 4k+1, например: 49 или 129)',
      defaultValue: 129,
      options: [
        { value: 49, label: '49 кадров (~2 секунды)' },
        { value: 129, label: '129 кадров (~5 секунд)' }
      ],
      order: 3,
      group: 'advanced'
    },

    // Quality settings
    infer_steps: {
      type: 'slider',
      label: 'Шаги генерации',
      description: 'Количество шагов генерации. Большие значения улучшают качество, но требуют больше времени.',
      defaultValue: 50,
      min: 1,
      max: 100,
      step: 1,
      order: 4,
      group: 'advanced'
    },

    embedded_guidance_scale: {
      type: 'slider',
      label: 'Шкала направления',
      description: 'Контролирует, насколько точно модель следует запросу. Большие значения = больше соответствия.',
      defaultValue: 6,
      min: 1,
      max: 10,
      step: 0.5,
      order: 5,
      group: 'advanced'
    },

    // Frame rate
    fps: {
      type: 'number',
      label: 'FPS',
      description: 'Кадров в секунду выходного видео',
      defaultValue: 24,
      min: 1,
      max: 60,
      order: 6,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Случайное число для воспроизводимой генерации. Оставьте пустым для случайного значения.',
      order: 7,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: 'Расширенные настройки'
    }
  },

  metadata: {
    type: 'video',
    maxOutputs: 1,
    supportedFormats: ['mp4'],
    maxResolution: '1280x1280',
    estimatedTime: '2-5 minutes'
  }
};
