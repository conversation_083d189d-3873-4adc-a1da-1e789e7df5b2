import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const veo3Config: ModelInputConfig = {
  modelId: 'veo3',
  modelName: 'Veo 3',
  description: 'Флагманская модель Google Veo 3 для генерации видео из текста с возможностями генерации аудио',

  parameters: {
    // Prompt enhancement
    enhance_prompt: {
      ...CommonParameters.enhancePrompt,
      description: 'Использовать ИИ для улучшения вашего запроса, чтобы получить лучший результат',
      order: 1,
      group: 'advanced'
    },

    // Negative prompt
    negative_prompt: {
      ...CommonParameters.negativePrompt,
      description: 'Описание того, чего следует избегать',
      order: 2,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Случайное число для воспроизводимой генерации. Оставьте пустым для случайного значения.',
      order: 3,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: 'Расширенные настройки'
    }
  },

  metadata: {
    type: 'video',
    maxOutputs: 1,
    supportedFormats: ['mp4'],
    maxResolution: '4K',
    estimatedTime: '1-3 minutes'
  }
};
