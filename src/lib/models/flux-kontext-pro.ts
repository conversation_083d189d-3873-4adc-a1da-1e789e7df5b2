import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const fluxKontextProConfig: ModelInputConfig = {
  modelId: 'flux-kontext-pro',
  modelName: 'FLUX Kontext Pro',
  description: 'Современная модель редактирования изображений на основе текста, которая обеспечивает высококачественные результаты с отличным следованием промптам и стабильными результатами для преобразования изображений через естественный язык',

  parameters: {
    // Input image for editing
    input_image: {
      type: 'file',
      label: 'Входное изображение (необязательно)',
      description: 'Изображение для использования в качестве ссылки. Должно быть jpeg, png, gif или webp.',
      accept: 'image/*',
      order: 1,
      group: 'advanced'
    },

    // Aspect ratio with Kontext-specific options
    aspect_ratio: {
      type: 'select',
      label: 'Соотношение сторон',
      description: 'Соотношение сторон сгенерированного изображения',
      defaultValue: 'match_input_image',
      options: [
        { value: 'match_input_image', label: 'Соответствовать входному изображению' },
        { value: '1:1', label: '1:1 (Квадрат)' },
        { value: '16:9', label: '16:9 (Пейзаж)' },
        { value: '9:16', label: '9:16 (Мобильный портрет)' },
        { value: '4:3', label: '4:3 (Стандартный)' },
        { value: '3:4', label: '3:4 (Портрет)' },
        { value: '3:2', label: '3:2 (Фото)' },
        { value: '2:3', label: '2:3 (Фотопортрет)' },
        { value: '4:5', label: '4:5 (Портрет)' },
        { value: '5:4', label: '5:4 (Пейзаж)' },
        { value: '21:9', label: '21:9 (Сверхширокий)' },
        { value: '9:21', label: '9:21 (Высокий портрет)' },
        { value: '2:1', label: '2:1 (Широкий)' },
        { value: '1:2', label: '1:2 (Высокий)' }
      ],
      order: 2,
      group: 'advanced'
    },

    // Safety tolerance (unique to Kontext Pro)
    safety_tolerance: {
      type: 'slider',
      label: 'Допуск безопасности',
      description: 'Допуск безопасности: 0 - самый строгий, 6 - самый разрешительный. 2 в настоящее время является максимально допустимым при использовании входных изображений.',
      defaultValue: 2,
      min: 0,
      max: 6,
      step: 1,
      order: 3,
      group: 'advanced'
    },

    // Output format
    output_format: {
      type: 'select',
      label: 'Формат вывода',
      description: 'Формат выходных изображений',
      defaultValue: 'png',
      options: [
        { value: 'jpg', label: 'JPEG' },
        { value: 'png', label: 'PNG' }
      ],
      order: 4,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Случайное начальное значение. Устанавливается для воспроизводимой генерации',
      order: 5,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: 'Расширенные настройки'
    }
  },

  metadata: {
    type: 'image',
    maxOutputs: 1,
    supportedFormats: ['jpg', 'png'],
    maxResolution: '2K',
    estimatedTime: '10-30 seconds'
  }
};
