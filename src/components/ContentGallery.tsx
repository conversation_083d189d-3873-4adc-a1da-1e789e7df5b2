import React from 'react';
import { Play, Download, Heart, Share, MoreHorizontal } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const ContentGallery = () => {
  const generatedVideos = [
    {
      id: 1,
      thumbnail: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop",
      title: "Mountain Sunrise",
      duration: "5s",
      likes: 24,
      views: "1.2K"
    },
    {
      id: 2,
      thumbnail: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=200&fit=crop",
      title: "Forest Path",
      duration: "8s",
      likes: 18,
      views: "856"
    },
    {
      id: 3,
      thumbnail: "https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=300&h=200&fit=crop",
      title: "Ocean Waves",
      duration: "10s",
      likes: 32,
      views: "2.1K"
    },
    {
      id: 4,
      thumbnail: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop",
      title: "City Lights",
      duration: "6s",
      likes: 45,
      views: "3.4K"
    },
    {
      id: 5,
      thumbnail: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=200&fit=crop",
      title: "Desert Sunset",
      duration: "7s",
      likes: 28,
      views: "1.8K"
    },
    {
      id: 6,
      thumbnail: "https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=300&h=200&fit=crop",
      title: "Snowy Mountains",
      duration: "12s",
      likes: 67,
      views: "4.2K"
    }
  ];

  return (
    <div className="space-y-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Недавние Творения</h2>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-gray-600 text-sm">Сортировать по:</span>
            <select className="border border-gray-200 rounded-lg px-3 py-1 text-sm ">
              <option>Недавние</option>
              <option>Популярность</option>
              <option>Просматриваемости</option>
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-gray-600 text-sm">Фильтр:</span>
            <select className="border border-gray-200 rounded-lg px-3 py-1 text-sm ">
              <option>Все</option>
              <option>Видео</option>
              <option>Изображения</option>
            </select>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {generatedVideos.map((video) => (
          <Card key={video.id} className="bg-white border border-gray-200 overflow-hidden group hover:shadow-lg transition-all duration-300">
            <div className="relative">
              <img
                src={video.thumbnail}
                alt={video.title}
                className="w-full h-48 object-cover"
              />
              <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                <button className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors">
                  <Play className="w-6 h-6 text-white ml-1" />
                </button>
              </div>
              <div className="absolute top-3 right-3 bg-black/50 backdrop-blur-sm rounded-lg px-2 py-1">
                <span className="text-white text-xs font-medium">{video.duration}</span>
              </div>
              <div className="absolute top-3 left-3">
                <button className="w-8 h-8 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                  <MoreHorizontal className="w-4 h-4 text-white" />
                </button>
              </div>
            </div>
            <CardContent className="p-4">
              <h3 className="text-gray-900 font-medium mb-2 text-sm">{video.title}</h3>
              <div className="flex items-center justify-between text-xs text-gray-500">
                <div className="flex items-center space-x-3">
                  <span>{video.views} просмотров</span>
                  <div className="flex items-center space-x-1">
                    <Heart className="w-3 h-3" />
                    <span>{video.likes}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="text-gray-400 hover:text-gray-600 transition-colors">
                    <Share className="w-4 h-4" />
                  </button>
                  <button className="text-gray-400 hover:text-gray-600 transition-colors">
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ContentGallery;
