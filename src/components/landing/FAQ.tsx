'use client';

import { useState } from 'react';
import { ChevronDown } from 'lucide-react';

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const faqs = [
    {
      question: 'Что такое AI-генерация контента?',
      answer: 'Это технология, которая использует искусственный интеллект для создания уникальных изображений, видео и других визуальных материалов на основе ваших текстовых описаний. Вы описываете идею словами —  Холст.ИИ  превращает её в готовый контент.'
    },
    {
      question: 'Сколько времени занимает генерация контента?',
      answer: 'Большинство изображений генерируются за 30-60 секунд. Время создания видео зависит от его длины и сложности, но наша платформа оптимизирована, что дает максимальную скорость.'
    },
    {
      question: 'Какие форматы файлов поддерживаются?',
      answer: 'Мы поддерживаем все популярные форматы: PNG, WebP и JPEG для изображений, MP4, WebM для видео. Вы можете выбрать высокое разрешение для печати или оптимизированные версии для соцсетей.'
    },
    {
      question: 'Могу ли я использовать созданный контент в коммерческих целях?',
      answer: 'Да, конечно. С нашей коммерческой лицензией вы получаете полные права на использование созданного контента в любых целях, включая рекламу, маркетинг и продажу продуктов.'
    },
    {
      question: 'Как происходит оплата?',
      answer: 'Вы покупаете кредиты, которые далее расходуете в процессе генерации контента. Оплату можно производить картой или по купону.'
    },
    {
      question: 'Что происходит с моими данными и созданным контентом?',
      answer: 'Ваши данные и созданный контент полностью конфиденциальны. Они принадлежат только вам и хранятся на защищенных серверах. Мы никогда не используем их без вашего разрешения.'
    },
    {
      question: 'Какая у вас политика возврата средств?',
      answer: 'Мы стремимся к тому, чтобы вы были довольны результатом. Если у вас возникли проблемы, свяжитесь с нашей службой поддержки, и мы рассмотрим вашу ситуацию индивидуально. Подробнее — в наших Условиях Использования.'
    }
  ];

  return (
    <section id="faq" className="py-20 lg:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-4 text-text-dark">
            Часто Задаваемые{' '}
            <span className="bg-gradient-to-r from-blue-800 to-blue-400 bg-clip-text text-transparent">Вопросы</span>
          </h2>
          <p className="text-xl text-text-light max-w-3xl mx-auto">
            Всё, что нужно знать о  Холст.ИИ  — нашей платформе для создания контента с помощью искусственного интеллекта.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="card bg-background-primary rounded-xl mb-4 overflow-hidden animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <button
                className="w-full px-8 py-4 text-left flex items-center justify-between hover:bg-background-secondary transition-colors duration-200"
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
              >
                <h3 className="text-lg font-semibold text-text-dark pr-4">{faq.question}</h3>
                <ChevronDown
                  className={`w-6 h-6 text-text-light transition-transform duration-200 flex-shrink-0 ${
                    openIndex === index ? 'transform rotate-180' : ''
                  }`}
                />
              </button>

              {openIndex === index && (
                <div className="px-8 pb-4 pt-2">
                  <p className="text-text-light leading-relaxed">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQ;
