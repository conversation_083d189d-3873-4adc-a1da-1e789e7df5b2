import Link from 'next/link';

const Footer = () => {

  const legalLinks = [
    { name: 'Политика Конфиденциальности', href: '/privacy-policy', isRoute: true },
    { name: 'Условия Использования', href: '/terms-of-use', isRoute: true },
  ];

  const pageLinks = [
    { name: 'Создание контента для SMM', href: '/smm', isRoute: true },
    { name: 'Создание обложек для маркетплейсов', href: '/marketplace', isRoute: true },
    { name: 'Поиск вдохновения', href: '/inspiration', isRoute: true },
    { name: 'Оживление фотографий', href: '/living-photo', isRoute: true },
    { name: 'Создание инфлюэнсеров', href: '/influencer', isRoute: true },
  ];

  return (
    <footer className="bg-background-secondary">
      <div className="container mx-auto px-4 pb-8">
        {/* Bottom Bar */}
        <div className=" mt-12 pt-8">
          <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:max-w-[200px]">
              <p className="text-text-light text-sm">
                © 2025 Холст.ИИ.<br/> ИП Ступаков Ю.В.<br/> ИНН 773000356687. <br/>Все права защищены.
              </p>
            </div>

            {/* Page Links - Center */}
            <div className="flex flex-col items-center gap-2 mb-4 md:mb-0">
              {pageLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="text-text-light hover:text-primary transition-colors duration-200 text-sm"
                >
                  {link.name}
                </Link>
              ))}
            </div>

            {/* Legal Links - Right */}
            <div className="flex flex-col items-end gap-2">
              {legalLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="text-text-light hover:text-primary transition-colors duration-200 text-sm"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
