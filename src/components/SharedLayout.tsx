'use client';

import { usePathname } from 'next/navigation';
import React, { useState } from 'react';
import Sidebar from '@/components/Sidebar';
import { useIsMobile } from '@/hooks/use-mobile';
import { Menu } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SharedLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

const SharedLayout = ({ children, title, description }: SharedLayoutProps) => {
  const pathname = usePathname();
  const isMobile = useIsMobile();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Hide sidebar on authentication pages
  const authPages = ['/login/'];
  const showSidebar = !authPages.includes(pathname);

  if (!showSidebar) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto p-6">
          {(title || description) && (
            <div className="text-center mb-8">
              {title && <h1 className="text-4xl font-bold text-gray-900 mb-3">{title}</h1>}
              {description && <p className="text-gray-600 text-lg">{description}</p>}
            </div>
          )}
          {children}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white flex w-full">
      {/* Sidebar - fixed on desktop, Sheet on mobile */}
      {!isMobile ? (
        <div className="fixed left-0 top-0 h-full z-10 hidden md:block">
          <Sidebar />
        </div>
      ) : (
        <Sidebar isOpen={sidebarOpen} onOpenChange={setSidebarOpen} />
      )}

      {/* Main Content */}
      <main className={cn(
        "flex-1 overflow-auto bg-gray-50",
        isMobile ? "w-full" : "ml-64"
      )}>
        {/* Mobile Header with Menu Button */}
        {isMobile && (
          <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-4 py-3 flex items-center">
            <button
              onClick={() => setSidebarOpen(true)}
              className="p-2 rounded-md hover:bg-gray-100 mr-3"
            >
              <Menu className="w-6 h-6 text-gray-700" />
            </button>
            <div className="flex items-center">
              <img
                src="/ForLightBG.png"
                alt="Logo"
                className="w-7 h-7 object-contain"
              />
              <span className="ml-2 text-lg font-semibold text-gray-900">Холст.ИИ</span>
            </div>
          </div>
        )}

        <div className="max-w-7xl mx-auto p-6">
          {(title || description) && (
            <div className="text-center mb-8">
              {title && <h1 className="text-4xl font-bold text-gray-900 mb-3">{title}</h1>}
              {description && <p className="text-gray-600 text-lg">{description}</p>}
            </div>
          )}
          {children}
        </div>
      </main>
    </div>
  );
};

export default SharedLayout;
