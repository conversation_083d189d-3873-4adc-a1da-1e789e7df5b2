'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, Coins, Zap, Crown, Star, Image, Video } from 'lucide-react';
import SharedLayout from "@/components/SharedLayout";
import {useMutation} from "@tanstack/react-query";
import {api, mutationKeys} from "@/lib/api";
import {usePBContext} from "@/context/PocketbaseContext";
import {useIsMobile} from "@/hooks/use-mobile";
import {safeYMGoal} from "@/lib/utils";

const Pricing = () => {
  const { pb } = usePBContext();
  const isMobile = useIsMobile();

  // Token pricing tiers
  const tokenPackages = [
    {
      name: "100 Кредитов",
      tokens: 100,
      priceNumeric: "300",
      price: "300₽",
      pricePerToken: "3₽ за кредит",
      description: "Идеально для знакомства с нашими AI моделями",
      icon: Coins,
      popular: false,
      savings: null,
      buttonText: "Купить 100 Кредитов"
    },
    {
      name: "600 Кредитов",
      tokens: 600,
      priceNumeric: "1300",
      price: "1300₽",
      pricePerToken: "2.16₽ за кредит",
      description: "Отлично для регулярных творческих проектов",
      icon: Zap,
      popular: true,
      savings: "Скидка 28%",
      buttonText: "Купить 600 Кредитов"
    },
    {
      name: "1000 Кредитов",
      tokens: 1000,
      priceNumeric: "2000",
      price: "2000₽",
      pricePerToken: "2₽ за кредит",
      description: "Лучшее предложение для активных пользователей",
      icon: Crown,
      popular: false,
      savings: "Скидка 33%",
      buttonText: "Купить 1000 Кредитов"
    }
  ];

  // Per-model token costs based on actual database prices
  const modelCosts = {
    image: [
      {
        name: "Seedream 3",
        id: 'seedream-3',
        cost: "3 кредита",
        description: "Поддержка нативного разрешения 2K",
        pricing: "3 кредита за изображение"
      },
      {
        name: "Imagen 4 Ultra",
        id: 'imagen-4-ultra',
        cost: "6 кредитов",
        description: "Ультра высокое разрешение изображений",
        pricing: "6 кредитов за изображение"
      },
      {
        name: "FLUX Kontext Pro",
        id: 'flux-kontext-pro',
        cost: "4 кредита",
        description: "Профессиональное редактирование изображений",
        pricing: "4 кредита за изображение"
      }
    ],
    video: [
      {
        name: "Seedance Lite",
        id: 'seedance-lite',
        variants: [
          { resolution: '480p', cost: 2, description: "2 кредита × длительность (секунды)" },
          { resolution: '1080p', cost: 4, description: "4 кредита × длительность (секунды)" }
        ],
        description: "Быстрая генерация видео до 10 секунд"
      },
      {
        name: "Seedance Pro",
        id: 'seedance-pro',
        variants: [
          { resolution: '480p', cost: 3, description: "3 кредита × длительность (секунды)" },
          { resolution: '1080p', cost: 15, description: "15 кредитов × длительность (секунды)" }
        ],
        description: "Качественная генерация видео до 10 секунд"
      },
      {
        name: "Hunyuan Video",
        id: 'hunyuan',
        cost: "250 кредитов",
        description: "Высококачественная генерация видео",
        pricing: "250 кредитов за видео"
      },
      {
        name: "Veo 2",
        id: 'veo2',
        cost: "50 кредитов",
        description: "Продвинутые возможности видео",
        pricing: "50 кредитов × длительность (секунды)"
      },
      {
        name: "Veo 3",
        id: 'veo3',
        cost: "75 кредитов",
        description: "Новейшие технологии генерации видео",
        pricing: "75 кредитов × 8 секунд = 600 кредитов за видео"
      },
      {
        name: "Veo 3 Fast",
        id: 'veo3-fast',
        cost: "40 кредитов",
        description: "Более быстрая и дешевая версия Veo3",
        pricing: "40 кредитов × 8 секунд = 320 кредитов за видео"
      }
    ]
  };

  const {
    mutate: createPayment,
    isPending: isCreatingPayment,
  } = useMutation({
    mutationKey: mutationKeys.createPayment,
    mutationFn: (request: {amount: string; referral?: string}) => {
      safeYMGoal('create_payment');
      return api.createPayment(pb, request);
    },
    onSuccess: (response) => {
      console.log(response);
      if (isMobile) {
        window.location.href = response.url;
      } else {
        window.open(response.url, '_blank');
      }
    },
    onError: (error) => {
      console.error('Payment creation error:', error);
    },
  });

  return (
    <SharedLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-gray-900">
            Простые{' '}
            <span className="text-[var(--primary-blue)]">цены</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Платите только за то, что используете. Никаких подписок, никаких скрытых комиссий. Покупайте кредиты и используйте их для всех наших AI моделей.
          </p>
        </div>

        {/* Token Packages */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto mb-16">
          {tokenPackages.map((pkg, index) => (
            <Card
              key={index}
              className={`relative border transition-all duration-300 hover:shadow-lg rounded-xl ${
                pkg.popular
                  ? 'border border-blue-400 hover:border-gray-300 shadow-lg scale-105'
                  : 'border border-gray-200 hover:border-gray-300'
              }`}
            >
              {pkg.popular && (
                <div className="absolute -top-7 md:-top-4 left-1/2 transform -translate-x-1/2 text-center">
                  <div className="bg-blue-600 text-white px-2 py-2 rounded-full text-xs whitespace-nowrap font-semibold flex items-center gap-1">
                    <Star className="w-4 h-4 fill-current" />
                    Самый популярный
                  </div>
                </div>
              )}

              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                    pkg.popular
                      ? 'bg-[var(--primary-blue)]'
                      : 'bg-gray-100'
                  }`}>
                    <pkg.icon className={`w-6 h-6 ${pkg.popular ? 'text-white' : 'text-gray-600'}`} />
                  </div>
                </div>
                <CardTitle className="text-xl text-gray-900">{pkg.name}</CardTitle>
                <p className="text-gray-600 text-sm">{pkg.description}</p>
              </CardHeader>

              <CardContent className="text-center">
                <div className="mb-4">
                  <div className="text-3xl font-bold text-gray-900 mb-1">{pkg.price}</div>
                  <div className="text-sm text-gray-500">
                    {pkg.pricePerToken}
                    {pkg.savings && (
                      <span className="block text-green-600 font-semibold mt-1">
                        {pkg.savings}
                      </span>
                    )}
                  </div>
                </div>

                <button
                  onClick={() => createPayment({amount: pkg.priceNumeric, referral: localStorage.getItem('referral')})}
                  disabled={isCreatingPayment}
                  className={`w-full ${
                    pkg.popular
                      ? 'bg-blue-500 hover:bg-blue-700 text-white px-4 py-2 rounded-xl font-semibold text-white'
                      : 'border-2 border-blue-600 bg-white px-4 py-2 rounded-xl font-semibold text-blue-500'
                  }`}
                >
                  {pkg.buttonText}
                </button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Model Pricing Section */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4 text-gray-900">
            Стоимость <span className="text-[var(--primary-blue)]">по моделям</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Разные AI модели требуют различных вычислительных ресурсов. Вот сколько кредитов стоит каждая модель за генерацию.
          </p>
        </div>

        {/* Image Models */}
        <div className="mb-12">
          <div className="flex items-center justify-center mb-8">
            <Image className="w-6 h-6 text-[var(--primary-blue)] mr-2" />
            <h3 className="text-2xl font-bold text-gray-900">Модели Генерации Изображений</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl mx-auto">
            {modelCosts.image.map((model, index) => (
              <Card key={model.id} className="border border-gray-200 shadow-sm">
                <CardContent className="p-6 text-center">
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">{model.name}</h4>
                  <div className="text-2xl font-bold text-[var(--primary-blue)] mb-2">
                    {model.cost}
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{model.description}</p>
                  <p className="text-xs text-gray-500">{model.pricing}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Video Models */}
        <div className="mb-12">
          <div className="flex items-center justify-center mb-8">
            <Video className="w-6 h-6 text-[var(--primary-blue)] mr-2" />
            <h3 className="text-2xl font-bold text-gray-900">Модели Генерации Видео</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl mx-auto">
            {modelCosts.video.map((model, index) => (
              <Card key={model.id} className="border border-gray-200 shadow-sm rounded-xl">
                <CardContent className="p-6">
                  <div className="text-center mb-4">
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">{model.name}</h4>
                    <p className="text-sm text-gray-600">{model.description}</p>
                  </div>

                  {model.variants ? (
                    // Models with resolution variants (Seedance)
                    <div className="space-y-3">
                      {model.variants.map((variant, variantIndex) => (
                        <div key={variantIndex} className="border border-gray-200 rounded-lg p-3 bg-gray-50">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-sm font-medium text-gray-900">{variant.resolution}</span>
                            <span className="text-lg font-bold text-[var(--primary-blue)]">
                              {variant.cost}
                            </span>
                          </div>
                          <p className="text-xs text-gray-600">{variant.description}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    // Models with fixed pricing
                    <div className="text-center mt-12">
                      <div className="text-2xl font-bold text-[var(--primary-blue)] mb-2">
                        {model.cost}
                      </div>
                      <p className="text-xs text-gray-600">{model.pricing}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Pricing Examples Section */}
        <div className="mb-12">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900">
              Примеры <span className="text-[var(--primary-blue)]">Цен</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Посмотрите, сколько стоят обычные задачи генерации с нашей кредитной системой.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl mx-auto">
            {/* Image Examples */}
            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Image className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Одно Изображение</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-600">FLUX Dev (1 изображение): 3 кредита</div>
                  <div className="text-gray-600">Seedream 3 (1 изображение): 3 кредита</div>
                  <div className="text-gray-600">Imagen 4 Ultra (1 изображение): 6 кредитов</div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Image className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Пакетная Генерация</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-600">FLUX Dev (4 изображения): 12 кредитов</div>
                  <div className="text-gray-600">Seedream 3 (4 изображения): 12 кредитов</div>
                  <div className="text-gray-600">Imagen 4 Ultra (4 изображения): 24 кредита</div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Video className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Короткие Видео</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-600">Seedance Lite 480p (5с): 10 кредитов</div>
                  <div className="text-gray-600">Seedance Pro 480p (5с): 15 кредитов</div>
                  <div className="text-gray-600">Veo 2 (5с): 250 кредитов</div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Video className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">HD Видео</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-600">Seedance Lite 1080p (5с): 20 кредитов</div>
                  <div className="text-gray-600">Seedance Pro 1080p (5с): 75 кредитов</div>
                  <div className="text-gray-600">Hunyuan Video: 250 кредитов</div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Video className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Премиум Видео</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-600">Veo 3 (8с фиксированно): 600 кредитов</div>
                  <div className="text-gray-600">Veo 2 (10с): 500 кредитов</div>
                  <div className="text-gray-600">Seedance Pro 1080p (10с): 150 кредитов</div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Coins className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Сравнение Стоимости</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="text-[var(--primary-blue)] mb-2">С 600 кредитами (1300₽):</div>
                  <div className="text-gray-600">~200 изображений Seedream 3: 6.5₽ за каждое</div>
                  <div className="text-gray-600">~30 видео Seedance Lite (5с): 43₽ за каждое</div>
                  <div className="text-gray-600">~2 видео Hunyuan: 650₽ за каждое</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">
            Часто Задаваемые Вопросы
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-3">Истекают ли кредиты?</h3>
                <p className="text-gray-600">
                  Нет, ваши кредиты никогда не истекают. Купите их один раз и используйте, когда захотите создавать контент
                </p>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-3">Как рассчитывается стоимость видео?</h3>
                <p className="text-gray-600">
                   Стоимость видео зависит от модели, разрешения и длительности. Например, модели Seedance берут плату за секунду, а Veo 3 имеет фиксированную продолжительность — 8 секунд
                </p>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-3">Что происходит если генерация не удается?</h3>
                <p className="text-gray-600">
                  Если генерация не удается из-за ошибки нашей системы, ваши кредиты будут автоматически возвращены на ваш счет.
                </p>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-3">Почему у моделей разные цены?</h3>
                <p className="text-gray-600">
                  Разные AI модели требуют различных вычислительных ресурсов. Более продвинутые модели, такие как Veo 3 и Hunyuan, стоят дороже из-за их превосходного качества и расширенных возможностей
                </p>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-3">Можно ли получить скидки за объем?</h3>
                <p className="text-gray-600">
                  Да! Большие пакеты кредитов предлагают лучшие цены за кредит. Пакет из 1000 кредитов экономит вам 33% по сравнению с пакетом из 100 кредитов.
                </p>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-3">Как отслеживать использование кредитов?</h3>
                <p className="text-gray-600">
                  Ваш текущий баланс кредитов отображается в вашем профиле, и каждая генерация показывает точную стоимость в кредитах перед подтверждением.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </SharedLayout>
  );
};

export default Pricing;
