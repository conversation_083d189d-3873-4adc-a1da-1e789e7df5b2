'use client';

import React, { useState } from 'react';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { ListResult } from 'pocketbase';
import { Clock, Image, Video, Download, ExternalLink, Copy } from 'lucide-react';
import SharedLayout from '@/components/SharedLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { usePBContext } from '@/context/PocketbaseContext';
import {GenerationRequestData, GenerationRequestStatus, queryKeys} from '@/lib/api';
import { toast } from '@/hooks/use-toast';

const GenerationHistory = () => {
  const { pb, user } = usePBContext();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [filter, setFilter] = useState<'all' | 'image' | 'video'>('all');

  const { data: historyData, isLoading: isLoadingHistory } = useQuery<ListResult<GenerationRequestData>>({
    queryKey: queryKeys.generationHistory(user?.id || '', currentPage, pageSize, filter),
    placeholderData: keepPreviousData,
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('No user ID found');
      }

      // Build filter string
      let filterString = `user = "${user.id}"`;
      if (filter !== 'all') {
        filterString += ` && type = "${filter}"`;
      }

      const result: ListResult<GenerationRequestData> = await pb.collection('generation_requests').getList(currentPage, pageSize, {
        filter: filterString,
        expand: 'output_files',
        sort: '-created',
      });
      return result;
    },
    enabled: !!user?.id
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusString = (status: GenerationRequestStatus) => {
    switch (status) {
      case "pending":
        return "Ожидание";
      case "completed":
        return "Завершено";
      case "failed":
        return "Ошибка"
      case "stalled":
        return "Зависло"
      case "canceled":
        return "Отменено"

    }
  }

  const getTypeIcon = (type: string) => {
    return type === 'video' ? Video : Image;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: string) => {
    setPageSize(parseInt(newPageSize));
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const handleFilterChange = (newFilter: 'all' | 'image' | 'video') => {
    setFilter(newFilter);
    setCurrentPage(1); // Reset to first page when changing filter
  };

  const handleCopyPrompt = async (prompt: string) => {
    try {
      await navigator.clipboard.writeText(prompt);
      toast({
        title: 'Промпт скопирован!',
        description: 'Промпт был скопирован в буфер обмена.',
        variant: 'default',
      });
    } catch (error) {
      toast({
        title: 'Ошибка копирования',
        description: 'Не удалось скопировать промпт. Попробуйте еще раз.',
        variant: 'destructive',
      });
    }
  };

  const renderPaginationItems = () => {
    if (!historyData) return null;

    const totalPages = historyData.totalPages;
    const items = [];

    // Previous button
    items.push(
      <PaginationItem key="prev">
        <PaginationPrevious
          onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
          className={currentPage <= 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
        />
      </PaginationItem>
    );

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
      items.push(
        <PaginationItem key={1}>
          <PaginationLink onClick={() => handlePageChange(1)} className="cursor-pointer">
            1
          </PaginationLink>
        </PaginationItem>
      );
      if (startPage > 2) {
        items.push(<PaginationItem key="ellipsis1"><PaginationEllipsis /></PaginationItem>);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <PaginationItem key={i}>
          <PaginationLink
            onClick={() => handlePageChange(i)}
            isActive={currentPage === i}
            className="cursor-pointer"
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        items.push(<PaginationItem key="ellipsis2"><PaginationEllipsis /></PaginationItem>);
      }
      items.push(
        <PaginationItem key={totalPages}>
          <PaginationLink onClick={() => handlePageChange(totalPages)} className="cursor-pointer">
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    // Next button
    items.push(
      <PaginationItem key="next">
        <PaginationNext
          onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
          className={currentPage >= totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
        />
      </PaginationItem>
    );

    return items;
  };

  return (
    <SharedLayout
      title="История генераций"
      description="Просмотрите все ваши предыдущие генерации видео и изображений"
    >
      <div className="space-y-6">
        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-2 mb-6">
          <Button
            variant={filter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleFilterChange('all')}
            className="flex items-center gap-2"
          >
            Все
          </Button>
          <Button
            variant={filter === 'image' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleFilterChange('image')}
            className="flex items-center gap-2"
          >
            <Image className="w-4 h-4" />
            Изображения
          </Button>
          <Button
            variant={filter === 'video' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleFilterChange('video')}
            className="flex items-center gap-2"
          >
            <Video className="w-4 h-4" />
            Видео
          </Button>
        </div>

        {/* Page Size Selector */}
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            {historyData && (
              <span>
                Показано {((currentPage - 1) * pageSize) + 1} до {Math.min(currentPage * pageSize, historyData.totalItems)} из {historyData.totalItems} генераций
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Показать:</span>
            <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="6">6</SelectItem>
                <SelectItem value="12">12</SelectItem>
                <SelectItem value="24">24</SelectItem>
                <SelectItem value="48">48</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm text-gray-600">на странице</span>
          </div>
        </div>

        {isLoadingHistory ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-32 bg-gray-200 rounded mb-4"></div>
                  <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : historyData?.items.length === 0 ? (
          <div className="text-center py-12">
            <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Пока нет генераций</h3>
            <p className="text-gray-500">Начните создавать видео и изображения, чтобы увидеть историю здесь.</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {historyData?.items.map((generation) => {
                const TypeIcon = getTypeIcon(generation.type || 'image');
                const prompt = generation.jsondata?.prompt || 'No prompt available';
                const hasOutputFiles = generation.expand?.output_files && generation.expand.output_files.length > 0;

                return (
                  <Card key={generation.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <TypeIcon className="h-4 w-4 text-gray-600" />
                          <CardTitle className="text-sm font-medium capitalize">
                            {generation.type === 'video' ? 'Видео' : generation.type === 'image' ? 'Изображение' : 'Неизвестно'}
                          </CardTitle>
                        </div>
                        <Badge className={getStatusColor(generation.status)}>
                          {getStatusString(generation.status)}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500">
                        {formatDate(generation.created)}
                      </p>
                    </CardHeader>
                    <CardContent className="pt-0">
                      {hasOutputFiles && generation.expand?.output_files?.[0] ? (
                        <div className="mb-4">
                          {generation.type === 'video' ? (
                            <video
                              className="w-full h-32 object-cover rounded-md"
                              controls
                              preload="metadata"
                            >
                              <source src={pb.files.getURL(generation.expand.output_files[0], generation.expand.output_files[0].file)} />
                            </video>
                          ) : (
                            <img
                              src={pb.files.getURL(generation.expand.output_files[0], generation.expand.output_files[0].file)}
                              alt="Generated content"
                              className="w-full h-32 object-cover rounded-md"
                            />
                          )}
                        </div>
                      ) : (
                        <div className="w-full h-32 bg-gray-100 rounded-md mb-4 flex items-center justify-center">
                          <TypeIcon className="h-8 w-8 text-gray-400" />
                        </div>
                      )}

                      <div className="mb-3">
                        <div className="flex items-start justify-between gap-2">
                          <p className="text-sm text-gray-700 line-clamp-2 flex-1">
                            {prompt}
                          </p>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyPrompt(prompt)}
                            className="h-6 w-6 p-0 flex-shrink-0 hover:bg-gray-100"
                            title="Копировать промпт"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      {generation.error && (
                        <p className="text-xs text-red-300">Ошибка: {generation.error}</p>
                      )}
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>Модель: {generation.model}</span>
                        {hasOutputFiles && (
                          <div className="flex space-x-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 px-2"
                              onClick={() => {
                                const url = pb.files.getURL(generation.expand!.output_files![0], generation.expand!.output_files![0].file);
                                window.open(url, '_blank');
                              }}
                            >
                              <ExternalLink className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 px-2"
                              onClick={() => {
                                const url = pb.files.getURL(generation.expand!.output_files![0], generation.expand!.output_files![0].file);
                                const link = document.createElement('a');
                                link.href = url;
                                link.download = `generation-${generation.id}`;
                                link.click();
                              }}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Pagination */}
            {historyData && historyData.totalPages > 1 && (
              <div className="flex justify-center mt-8">
                <Pagination>
                  <PaginationContent>
                    {renderPaginationItems()}
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </>
        )}
      </div>
    </SharedLayout>
  );
};

export default GenerationHistory;
