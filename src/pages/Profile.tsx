'use client';

import React, {useEffect, useState} from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, CreditCard, Download, Eye, CheckCircle, X, Copy, Share, Link } from 'lucide-react';
import SharedLayout from '@/components/SharedLayout';
import {usePBContext} from "@/context/PocketbaseContext";
import { useRouter, useSearchParams } from 'next/navigation';
import {useMutation, useQuery} from "@tanstack/react-query";
import {api, mutationKeys, queryKeys} from "@/lib/api";
import { useToast } from '@/hooks/use-toast';
import {safeYMGoal} from "@/lib/utils";


const Profile = () => {
  const { user, pb, setUser } = usePBContext();
  const { toast } = useToast();

  const searchParams = useSearchParams();
  const router = useRouter();

  const [showCongratulations, setShowCongratulations] = useState(false);
  const [confettiPieces, setConfettiPieces] = useState<Array<{id: number, left: number, delay: number, color: string}>>([]);

  const handleLogout = () => {
    pb.authStore.clear();
    setUser(undefined);
    router.push('/');
  }

  // Generate referral link
  const referralLink = `${window.location.origin}/?referral=${user?.id}`;

  const handleCopyReferralLink = async () => {
    try {
      await navigator.clipboard.writeText(referralLink);
      toast({
        title: 'Реферальная ссылка скопирована!',
        description: 'Поделитесь этой ссылкой с друзьями, чтобы начать зарабатывать кредиты.',
        variant: 'default',
      });
    } catch (error) {
      toast({
        title: 'Ошибка копирования',
        description: 'Не удалось скопировать реферальную ссылку. Попробуйте еще раз.',
        variant: 'destructive',
      });
    }
  };

  const {data: userBalance } = useQuery({
    queryKey: queryKeys.userTokenBalance(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('No user ID found');
      }

      return await api.getUserTokenBalance(pb, user.id);
    },
    enabled: !!user?.id
  });

  // Handle successful payment redirect
  useEffect(() => {
    const topUpAmount = searchParams.get('topUp');
    if (topUpAmount) {
      // Generate confetti pieces
      const pieces = Array.from({ length: 20 }, (_, i) => ({
        id: i,
        left: Math.random() * 100,
        delay: Math.random() * 2,
        color: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][Math.floor(Math.random() * 5)]
      }));
      setConfettiPieces(pieces);
      setShowCongratulations(true);
      safeYMGoal('top_up_success',topUpAmount);
    }
  }, [searchParams]);

  const handleCloseCongratulations = () => {
    setShowCongratulations(false);
    setConfettiPieces([]);
    // Remove the query parameter from URL without triggering navigation
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.delete('topUp');

    // Use Next.js router to update URL
    const newUrl = `${window.location.pathname}${newSearchParams.toString() ? '?' + newSearchParams.toString() : ''}`;
    router.replace(newUrl);
  };

  return (
      <SharedLayout
          title="Профиль"
          description="Управляйте своим аккаунтом и просматривайте свои творения"
      >
        {/* Congratulations Modal */}
        {showCongratulations && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
            {/* Confetti */}
            {confettiPieces.map((piece) => (
              <div
                key={piece.id}
                className="absolute w-3 h-3 animate-confetti"
                style={{
                  left: `${piece.left}%`,
                  backgroundColor: piece.color,
                  // animationDelay: `${piece.delay}s`,
                  borderRadius: Math.random() > 0.5 ? '50%' : '0%'
                }}
              />
            ))}

            {/* Modal Content */}
            <div className="relative bg-white rounded-2xl p-8 max-w-md mx-4 text-center animate-bounce-in shadow-2xl">
              <button
                onClick={handleCloseCongratulations}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>

              <div className="mb-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-success">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Платеж успешен! 🎉
                </h2>
                <p className="text-gray-600 mb-2">
                  Ваш аккаунт пополнен на {searchParams.get('topUp') || '0'} кредитов!
                </p>
                <p className="text-sm text-gray-500">
                  Спасибо за покупку. Теперь вы можете создавать потрясающий контент с ИИ!
                </p>
              </div>

              <button
                onClick={handleCloseCongratulations}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg"
              >
                Продолжить создание
              </button>
            </div>
          </div>
        )}
        <div className="space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-stretch">
            {/* Profile Information */}
            <div className="lg:col-span-2 flex">
              <Card className="border border-gray-200 shadow-sm flex-1">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <User className="w-5 h-5" />
                    <span>Информация профиля</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Имя</label>
                      <Input
                          value={user.name}
                          className="border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                          disabled={true}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                      <Input
                          type="email"
                          value={user.email}
                          className="border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                          disabled={true}
                      />
                    </div>
                  </div>
                  <button
                      onClick={handleLogout}
                      className="w-full bg-[var(--primary-blue)] hover:bg-[var(--primary-blue-hover)] text-white px-6 py-2 rounded-lg font-medium text-sm"
                  >
                    Выйти
                  </button>
                </CardContent>
              </Card>
            </div>

            {/* Account Stats */}
            <div className="flex">
              <Card className="border border-gray-200 shadow-sm flex-1">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CreditCard className="w-5 h-5" />
                    <span>Кредиты</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 mb-2">
                      {userBalance}
                    </div>
                    <p className="text-gray-600 mb-4">Кредитов осталось</p>
                    <button onClick={() => {
                      safeYMGoal('go_to_pricing');
                      router.push('/pricing');
                      // createPayment({amount: '300'})
                    }} className="w-full bg-[var(--primary-blue)] hover:bg-[var(--primary-blue-hover)] text-white px-6 py-2 rounded-lg font-medium text-sm">
                      Пополнить
                    </button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Referral Program Section */}
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Share className="w-5 h-5" />
                <span>Реферальная программа</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Offer highlight */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <CreditCard className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">
                      Приглашайте друзей и зарабатывайте кредиты вместе!
                    </h4>
                    <p className="text-sm text-gray-700">
                      Получайте 50% от кредитов, которые покупает ваш друг, и 25 кредитов за каждого зарегистрированного друга
                    </p>
                  </div>
                </div>
              </div>

              {/* Referral link */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Ваша реферальная ссылка
                </label>
                <div className="flex space-x-2">
                  <Input
                    value={referralLink}
                    readOnly
                    className="flex-1 bg-gray-50 border-gray-200 text-sm"
                  />
                  <button
                    onClick={handleCopyReferralLink}
                    className="border bolder-gray-200 flex items-center rounded-lg px-3 text-sm font-medium space-x-2"
                  >
                    <Copy className="w-4 h-4" />
                    <span>Копировать ссылку</span>
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </SharedLayout>
  );
};

export default Profile;
