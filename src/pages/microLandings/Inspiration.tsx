import Header from '@/components/landing/Header';
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import FAQ from '@/components/landing/FAQ';
import Footer from '@/components/landing/Footer';

const features = [
    {
        title: 'Подбор визуальных референсов по описанию',
        description: 'Просто опишите атмосферу или ключевые детали — ИИ создаст подборку уникальных картинок, идеально передающих нужное настроение.',
        image: 'inspiration/1.webp',
        reverse: false
    },
    {
        title: 'Генерация цветовых палитр',
        description: 'На основе вашей идеи ИИ предложит гармоничные цветовые сочетания с примерами их применения в дизайне и фото.',
        image: 'inspiration/2.webp',
        reverse: true
    },
    {
        title: 'Поиск стиля через смешение направлений',
        description: 'Опишите два или больше стиля (например, «ретро» и «минимализм») — ИИ создаст уникальные гибридные визуалы, которые могут стать новым фирменным стилем.',
        image: 'inspiration/3.webp',
        reverse: false
    },
    {
        title: 'Визуализация концепций до фотосессии',
        description: 'ИИ поможет увидеть, как будет выглядеть проект или продукт, ещё до съёмок или дизайна, чтобы избежать ошибок и быстрее согласовать идею.',
        image: 'inspiration/4.webp',
        reverse: true
    },
    {
        title: 'Случайные креативные генерации',
        description: 'Когда нет конкретной идеи, ИИ может выдавать неожиданные, но визуально цепляющие комбинации, которые становятся отправной точкой для нового проекта.',
        image: 'inspiration/5.webp',
        reverse: false
    }
]

const LandingInspiration = () => {
    return (
        <div className="min-h-screen bg-background-primary">
            <Header />
            <Hero title="Вдохновение" titleHighlight="на расстоянии одного клика" subtitle="Больше не нужно тратить часы на Pinterest и бесконечный скролл соцсетей. Опишите настроение, тему или идею — и искусственный интеллект подберёт образы, цветовые палитры и композиции, которые помогут запустить креативный процесс и найти новое направление для ваших проектов." secondaryImage='/heroImages/inspiration/landingHero2.jpg' topSecondaryImage='/heroImages/inspiration/landingHero3.jpg' bottomSecondaryImage='/heroImages/inspiration/landingHero4.jpg'/>
            <Features features={features}/>
            <FAQ />
            <Footer />
        </div>
    );
};

export default LandingInspiration;
