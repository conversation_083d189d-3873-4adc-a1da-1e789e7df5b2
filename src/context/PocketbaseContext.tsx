'use client';

import React, {create<PERSON>ontext, PropsWithChildren, useContext, useEffect, useMemo, useState} from 'react';
import PocketBase, {UnsubscribeFunc} from "pocketbase";

// Use Next.js environment variables with fallback to Vite for compatibility
const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || (typeof window !== 'undefined' && (window as any).VITE_BACKEND_URL) || process.env.VITE_BACKEND_URL

export interface User {
  id: string;
  created: string;
  updated: string;
  name: string;
  email: string;
  emailVisibility: boolean;
  avatar: string;
  tokens: number;
}

export interface IPocketbaseContext {
  pb: PocketBase;
  user?: User;
  setUser: (user: User | undefined) => void;
}

const PocketbaseContext = createContext<IPocketbaseContext | undefined>(undefined);

export const usePBContext = () => {
  const context = useContext(PocketbaseContext);
  if (!context) {
    throw new Error('usePBContext must be used within a PocketbaseProvider');
  }
  return context;
};

export const PocketbaseProvider: React.FC<PropsWithChildren> = ({children}) => {


  const pb = useMemo(() => {
    console.log(`creating new pb instance for ${BACKEND_URL}`)
    const p = new PocketBase(BACKEND_URL);
    p.autoCancellation(false);
    return p;
  }, [BACKEND_URL]);


  const [user, setUser] = useState<User | undefined>(pb.authStore.record as unknown as User);

  useEffect(() => {
    if (user) {
      try {
        //@ts-ignore
        jivo_api.setContactInfo({
          name: user.name,
          email: user.email
        });
      } catch (e) {
        console.log("error while calling jivo", e)
      }
    }
  }, [user]);

  let userUnsub: UnsubscribeFunc | undefined = undefined
  useEffect(() => {
    if (pb.authStore.isValid) {
      console.log('auth store is valid, refreshing auth')
      pb.collection('users').authRefresh().then((authResp)=>{
        console.log('refreshed auth', authResp)
        setUser(authResp.record as unknown as User);
      })

      if (userUnsub) {
        console.debug("unsubscribe beginning of user updates")
        userUnsub()
      }
      const model = pb.authStore.record as any | null;
      if (model) {
        pb.collection("users").subscribe(model.id, ({action, record}) => {
          console.debug(`received realtime user action - '${action}'`, record)

          if (action == "update") {
            console.debug('Received user update, setting user')
            setUser(record as unknown as User)
          }
        }, {}).then((f) => {
          userUnsub = f
        }).catch((err) => {
          console.error("Error while subscribing to user", err)
        })
      }
      return () => {
        if (userUnsub) {
          console.debug("unsubscribe from user updates")
          userUnsub()
        }
      }
    }
  }, [pb.authStore.isValid]);


  return (
    <PocketbaseContext.Provider value={{pb, user, setUser}}>
      {children}
    </PocketbaseContext.Provider>
  );
};
