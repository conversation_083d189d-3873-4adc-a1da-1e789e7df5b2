{"name": "unclutter-ui-forge", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "next dev -p 5173 --turbopack", "build": "next build", "start": "next start -p 5173  --turbopack", "export": "next build", "build:static": "next build", "preview": "next start"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-oauth/google": "^0.12.2", "@tanstack/query-sync-storage-persister": "^5.81.2", "@tanstack/react-query": "^5.56.2", "@tanstack/react-query-persist-client": "^5.81.2", "@vkid/sdk": "^2.6.0", "caniuse-lite": "^1.0.30001735", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "lucide-react": "^0.462.0", "next": "15.4.6", "next-themes": "^0.3.0", "pocketbase": "^0.26.1", "react": "^18.3.1", "react-cookie": "^8.0.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^3.1.2", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.20", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3"}}