const fs = require('fs');

// Read the VideoGenerator.tsx file
const filePath = '/Users/<USER>/Documents/uncluttered-ui-forge/src/components/VideoGenerator.tsx';
let content = fs.readFileSync(filePath, 'utf8');

// Translation mappings from the Russian translation file
const translations = {
  // Model names and descriptions
  "t(`videoGenerator.models.${model.id}.name`, model.name)": "model.name",
  "t(`videoGenerator.models.${model.id}.description`, model.description)": "model.description",

  // Prompt section
  "t('videoGenerator.promptLabel')": "'Опишите что вы хотите создать'",
  "t('videoGenerator.promptPlaceholder')": "'Спокойный горный пейзаж на рассвете с туманом катящимся по долинам, кинематографическое освещение, ультра-детализированный...'",
  "t('videoGenerator.translatePrompt.label')": "'Перевести запрос на английский'",

  // Parameter labels and descriptions
  "t(`videoGenerator.parameters.labels.${name}`, config.label)": "config.label",
  "t(`videoGenerator.parameters.descriptions.${name}`, config.description)": "config.description",

  // Generate button and status messages
  "t('videoGenerator.generateButton')": "'Генерировать'",
  "t('videoGenerator.statusMessages.generating', { type: isGeneratingVideo ? t('videoGenerator.types.video') : t('videoGenerator.types.image') })": "`Генерируем ваше ${isGeneratingVideo ? 'видео' : 'изображение'}... Это может занять несколько секунд.`",
  "t('videoGenerator.statusMessages.generating', { type: activeGeneration.type === 'video' ? t('videoGenerator.types.video') : t('videoGenerator.types.image') })": "`Генерируем ваше ${activeGeneration.type === 'video' ? 'видео' : 'изображение'}... Это может занять несколько секунд.`",
  "t('videoGenerator.statusMessages.notEnoughTokens', {amount: estimatedTokens})": "`Недостаточно кредитов. Требуется: ${estimatedTokens} кредитов`",
  "t('videoGenerator.statusMessages.tokensAmount', {amount: estimatedTokens})": "` ${estimatedTokens} кредитов`",
  "t('videoGenerator.statusMessages.browserNotSupported')": "'Ваш браузер не поддерживает видео тег.'",
  "t('videoGenerator.statusMessages.generationFailed', {error: activeGeneration.error})": "`Генерация не удалась. Ошибка: ${activeGeneration.error}`",

  // Toast messages
  "t('videoGenerator.toastMessages.videoGenerationStarted')": "'Генерация видео началась'",
  "t('videoGenerator.toastMessages.imageGenerationStarted')": "'Генерация изображения началась'",
  "t('videoGenerator.toastMessages.generationStartedDescription', { type: t('videoGenerator.types.video') })": "'Ваше видео генерируется. Вы получите уведомление когда оно будет готово.'",
  "t('videoGenerator.toastMessages.generationStartedDescription', { type: t('videoGenerator.types.image') })": "'Ваше изображение генерируется. Вы получите уведомление когда оно будет готово.'",
  "t('videoGenerator.toastMessages.success')": "'Успех'",
  "t('videoGenerator.toastMessages.videoGeneratedSuccess', { type: longPolledActive.type === 'video' ? t('videoGenerator.tabs.video') : t('videoGenerator.tabs.image') })": "`${longPolledActive.type === 'video' ? 'Видео' : 'Изображение'} успешно создано!`",
  "t('videoGenerator.toastMessages.generationFailedDescription', {\n            type: longPolledActive.type === 'video' ? t('videoGenerator.tabs.video') : t('videoGenerator.tabs.image'),\n            error: longPolledActive.error\n          })": "`Сгенерировать ${longPolledActive.type === 'video' ? 'видео' : 'изображение'} не удалось. Ошибка: ${longPolledActive.error}`",
  "t('videoGenerator.toastMessages.failedToStart', { type: t('videoGenerator.types.video') })": "'Не удалось начать создать видео. Попробуйте еще раз.'",
  "t('videoGenerator.toastMessages.failedToStart', { type: t('videoGenerator.types.image') })": "'Не удалось начать создать изображение. Попробуйте еще раз.'",

  // Active generations
  "t('videoGenerator.activeGenerations.title')": "'Текущие генерации'",
  "t('videoGenerator.activeGenerations.videoGeneration')": "'Генерация видео'",
  "t('videoGenerator.activeGenerations.imageGeneration')": "'Генерация изображения'",
  "t('videoGenerator.activeGenerations.generatedImageAlt')": "'Созданное изображение'",
  "t('videoGenerator.activeGenerations.download')": "'Скачать'",

  // Types
  "t('videoGenerator.types.video')": "'видео'",
  "t('videoGenerator.types.image')": "'изображение'",
  "t('videoGenerator.tabs.video')": "'видео'",
  "t('videoGenerator.tabs.image')": "'изображение'"
};

// Apply replacements
for (const [search, replace] of Object.entries(translations)) {
  const regex = new RegExp(search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
  content = content.replace(regex, replace);
}

// Write the updated content back to the file
fs.writeFileSync(filePath, content, 'utf8');

console.log('Translation replacements completed!');
