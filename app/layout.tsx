
import {Metadata, Viewport} from 'next';
import Script from 'next/script';
import "@/src/index.css";
import React from "react";

export const viewport: Viewport = {
  themeColor: '#0f0f23',
  width: 'device-width',
  initialScale: 1,
  userScalable: false,
}
export const metadata: Metadata = {
  title: 'Холст.ИИ - AI генерация изображений и видео',
  description: 'Превращайте текстовые описания в уникальные изображения и видео за секунды. Наши AI модели — ваш новый инструмент для безграничного творчества.',
  authors: [{ name: 'Холст.ИИ' }],
  keywords: 'AI, искусственный интеллект, генерация изображений, генерация видео, text-to-image, text-to-video, нейросети, творчество, дизайн',
  robots: 'index, follow',
  alternates: {
    canonical: 'https://holstai.ru/',
    languages: {
      'ru': 'https://holstai.ru',
      'x-default': 'https://holstai.ru',
    },
  },
  openGraph: {
    type: 'website',
    title: 'Холст.ИИ - AI генерация изображений и видео',
    description: 'Превращайте текстовые описания в уникальные изображения и видео за секунды. Наши AI модели — ваш новый инструмент для безграничного творчества.',
    url: 'https://holstai.ru',
    images: [
      {
        url: 'https://holstai.ru/icon.png',
        alt: 'Холст.ИИ логотип',
      },
    ],
    siteName: 'Холст.ИИ',
    locale: 'ru_RU',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Холст.ИИ - AI генерация изображений и видео',
    description: 'Превращайте текстовые описания в уникальные изображения и видео за секунды. Наши AI модели — ваш новый инструмент для безграничного творчества.',
    images: ['https://holstai.ru/icon.png'],
  },
  icons: {
    icon: [
      { url: '/favicon-96x96.png', sizes: '96x96', type: 'image/png' },
      { url: '/favicon.svg', type: 'image/svg+xml' },
      { url: '/favicon.ico', type: 'image/x-icon' },
    ],
    shortcut: '/favicon.ico',
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180' },
    ],
  },
  manifest: '/site.webmanifest',
  other: {
    'yandex-verification': '4ca3eefb865dae58',
    'apple-mobile-web-app-title': 'Холст.ИИ',
    'language': 'Russian',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ru">
      <head>
        <link rel="sitemap" type="application/xml" href="/sitemap.xml" />

        {/* Structured Data - Organization */}
        <Script
          id="organization-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "Холст.ИИ",
              "alternateName": "Holst.AI",
              "url": "https://holstai.ru",
              "logo": "https://holstai.ru/icon.png",
              "description": "Превращайте текстовые описания в уникальные изображения и видео за секунды. Наши AI модели — ваш новый инструмент для безграничного творчества.",
              "email": "<EMAIL>",
              "address": {
                "@type": "PostalAddress",
                "addressCountry": "RU"
              },
              "sameAs": [
                "https://t.me/holstai_support_bot"
              ],
              "contactPoint": {
                "@type": "ContactPoint",
                "contactType": "customer support",
                "url": "https://t.me/holstai_support_bot",
                "availableLanguage": ["Russian", "English"]
              },
              "areaServed": {
                "@type": "Place",
                "name": "Global"
              },
              "knowsAbout": [
                "Artificial Intelligence",
                "AI Image Generation",
                "AI Video Generation",
                "Text-to-Image",
                "Text-to-Video",
                "Machine Learning",
                "Computer Vision",
                "Creative AI Tools",
                "Digital Art Generation",
                "Content Creation"
              ],
              "applicationCategory": "AI Content Generation",
              "operatingSystem": "Web Browser"
            })
          }}
        />

        {/* Structured Data - Service */}
        <Script
          id="service-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Service",
              "serviceType": "AI Content Generation Services",
              "provider": {
                "@type": "Organization",
                "name": "Холст.ИИ",
                "alternateName": "Holst.AI"
              },
              "hasOfferCatalog": {
                "@type": "OfferCatalog",
                "name": "AI Generation Services",
                "itemListElement": [
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "Service",
                      "name": "AI Image Generation",
                      "description": "Создавайте уникальные изображения из текстовых описаний с помощью передовых AI моделей",
                      "category": "Text-to-Image",
                      "audience": {
                        "@type": "Audience",
                        "audienceType": "Designers, Artists, Content Creators, Marketers"
                      }
                    }
                  },
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "Service",
                      "name": "AI Video Generation",
                      "description": "Генерируйте высококачественные видео из текстовых промптов с реалистичным движением",
                      "category": "Text-to-Video",
                      "audience": {
                        "@type": "Audience",
                        "audienceType": "Video Creators, Marketers, Content Producers"
                      }
                    }
                  },
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "Service",
                      "name": "Creative AI Studio",
                      "description": "Полнофункциональная студия для создания контента с множеством AI моделей",
                      "category": "Creative Tools",
                      "audience": {
                        "@type": "Audience",
                        "audienceType": "Creative Professionals, Businesses, Individuals"
                      }
                    }
                  }
                ]
              },
              "areaServed": {
                "@type": "Place",
                "name": "Global"
              },
              "availableLanguage": ["Russian", "English"]
            })
          }}
        />

        {/* Structured Data - WebApplication */}
        <Script
          id="webapp-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              "name": "Холст.ИИ",
              "alternateName": "Holst.AI",
              "url": "https://holstai.ru",
              "description": "Превращайте текстовые описания в уникальные изображения и видео за секунды. Наши AI модели — ваш новый инструмент для безграничного творчества.",
              "applicationCategory": "AI Content Generation",
              "operatingSystem": "Web Browser",
              "browserRequirements": "Requires JavaScript. Requires HTML5.",
              "permissions": "No special permissions required",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "RUB",
                "description": "Freemium model with token-based pricing"
              },
              "featureList": [
                "AI Image Generation",
                "AI Video Generation",
                "Multiple AI Models",
                "Text-to-Image Conversion",
                "Text-to-Video Conversion",
                "Creative Studio Interface",
                "Generation History",
                "User Profiles"
              ],
              "screenshot": "https://holstai.ru/screenshot.jpeg"
            })
          }}
        />

        {/* Structured Data - FAQ */}
        <Script
          id="faq-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "FAQPage",
              "mainEntity": [
                {
                  "@type": "Question",
                  "name": "Что такое AI-генерация контента?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "AI-генерация контента использует передовые модели машинного обучения для создания изображений, видео и другого визуального контента из текстовых описаний. Наш ИИ понимает ваши промпты и генерирует высококачественный, уникальный контент за секунды."
                  }
                },
                {
                  "@type": "Question",
                  "name": "Сколько времени занимает генерация контента?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Большинство изображений генерируется в течение 5-15 секунд, а видео обычно занимает 30-60 секунд. Время обработки может варьироваться в зависимости от сложности и текущей загрузки системы."
                  }
                },
                {
                  "@type": "Question",
                  "name": "Какие AI модели доступны?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Мы предлагаем различные AI модели для генерации изображений (Imagen-4-Ultra, FLUX Kontext Pro, Seedream-3) и видео (Veo 3, Veo 2, Hunyuan Video, Seedance Pro/Lite), каждая с уникальными возможностями и стилями."
                  }
                }
              ]
            })
          }}
        />
      </head>
      <body>
        {/* Jivo Chat */}
        <Script
          src="//code.jivo.ru/widget/gYlVtKtnY4"
          strategy="afterInteractive"
        />

        {/* Yandex.Metrika counter */}
        <Script
          id="yandex-metrika"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
            (function(m,e,t,r,i,k,a){
              m[i]=m[i]||function(){(m[i].a=m[i].a||[]).push(arguments)};
              m[i].l=1*new Date();
              for (var j = 0; j < document.scripts.length; j++) {if (document.scripts[j].src === r) { return; }}
              k=e.createElement(t),a=e.getElementsByTagName(t)[0],k.async=1,k.src=r,a.parentNode.insertBefore(k,a)
            })(window, document,'script','https://mc.yandex.ru/metrika/tag.js?id=103438288', 'ym');
        
            ym(103438288, 'init', {ssr:true, webvisor:true, clickmap:true, ecommerce:"dataLayer", accurateTrackBounce:true, trackLinks:true});
            `
          }}
        />

        <noscript>
          <div>
            <img src="https://mc.yandex.ru/watch/103438288" style={{position:'absolute', left:'-9999px'}} alt="" />
          </div>
        </noscript>

          {children}
      </body>
    </html>
  );
}
