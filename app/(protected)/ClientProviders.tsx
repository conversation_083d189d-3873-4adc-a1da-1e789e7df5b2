'use client';

import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient } from "@tanstack/react-query";
import { PersistQueryClientProvider } from "@tanstack/react-query-persist-client";
import { createSyncStoragePersister } from "@tanstack/query-sync-storage-persister";
import { CookiesProvider } from "react-cookie";
import { PocketbaseProvider } from '@/src/context/PocketbaseContext';
import React, { useState } from 'react';

export default function ClientProviders({
  children,
}: {
  children: React.ReactNode;
}) {
  const [queryClient] = useState(() => {
    const debug = process.env.NODE_ENV === 'development';
    return new QueryClient({
      defaultOptions: {
        queries: {
          gcTime: debug ? 50 : 1000 * 5, // 50 ms or 5 minutes
        },
      },
    });
  });

  const [persister] = useState(() => createSyncStoragePersister({
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
  }));


  return (
      <PersistQueryClientProvider
        client={queryClient}
        persistOptions={{ persister }}
      >
        <CookiesProvider>
          <PocketbaseProvider>
            <TooltipProvider>
              <Toaster />
              <Sonner />
              {children}
            </TooltipProvider>
          </PocketbaseProvider>
        </CookiesProvider>
      </PersistQueryClientProvider>
  );
}
