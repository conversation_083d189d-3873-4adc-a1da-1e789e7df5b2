'use client';

import React, {useEffect} from 'react';
import {useRouter} from 'next/navigation';
import {usePBContext} from "@/src/context/PocketbaseContext";
import {useReferral} from "@/src/hooks/use-referral";
import ClientProviders from "@/app/(protected)/ClientProviders";
import SharedLayout from "@/components/SharedLayout";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedLayoutContent: React.FC<ProtectedRouteProps> = ({children}) => {
  const {user} = usePBContext();
  const router = useRouter();

  useEffect(() => {
    if (!user) {
      router.replace('/login');
    }
  }, [user, router]);

  // import for effects
  const _ = useReferral();

  if (!user) {
    return (
    <SharedLayout>
      {children}
    </SharedLayout>
    );
  }

  return <>{children}</>;
};

const ProtectedLayout: React.FC<ProtectedRouteProps> = ({children}) => {
  return (
    <ClientProviders>
      <ProtectedLayoutContent>
        {children}
      </ProtectedLayoutContent>
    </ClientProviders>
  )
}

export default ProtectedLayout;
