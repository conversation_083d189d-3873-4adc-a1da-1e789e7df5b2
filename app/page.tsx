import type { Metadata } from 'next';
import Index from '@/src/pages/Index';

export const metadata: Metadata = {
  title: 'Холст.ИИ - Создавайте картинки и видео без VPN и ограничений',
  description: 'Создавайте изображения и видео без VPN и ограничений. Наши ИИ модели — ваш новый инструмент для безграничного творчества.',
  keywords: 'ИИ генерация, создание изображений, генерация видео, искусственный интеллект, нейросети, творчество, дизайн, контент',
  openGraph: {
    title: 'Холст.ИИ - Создавайте картинки и видео без VPN и ограничений',
    description: 'Создавайте изображения и видео без VPN и ограничений. Наши ИИ модели — ваш новый инструмент для безграничного творчества.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Холст.ИИ - Создавайте картинки и видео без VPN и ограничений',
    description: 'Создавайте изображения и видео без VPN и ограничений. Наши ИИ модели — ваш новый инструмент для безграничного творчества.',
  },
};

export default function HomePage() {
  return <Index />;
}
