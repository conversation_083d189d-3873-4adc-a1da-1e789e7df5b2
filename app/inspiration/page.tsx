import type { Metadata } from 'next';
import LandingInspiration from '@/src/pages/microLandings/Inspiration';

export const metadata: Metadata = {
  title: 'Поиск вдохновения с помощью ИИ - Холст.ИИ',
  description: 'Больше не нужно тратить часы на Pinterest и бесконечный скролл соцсетей. Опишите настроение, тему или идею — и искусственный интеллект подберёт образы, цветовые палитры и композиции.',
  keywords: 'вдохновение, креативные идеи, цветовые палитры, визуальные референсы, дизайн идеи, творческий процесс',
  openGraph: {
    title: 'Поиск вдохновения с помощью ИИ - Холст.ИИ',
    description: 'Больше не нужно тратить часы на Pinterest и бесконечный скролл соцсетей. Опишите настроение, тему или идею — и искусственный интеллект подберёт образы, цветовые палитры и композиции.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Поиск вдохновения с помощью ИИ - Холст.ИИ',
    description: 'Больше не нужно тратить часы на Pinterest и бесконечный скролл соцсетей. Опишите настроение, тему или идею — и искусственный интеллект подберёт образы, цветовые палитры и композиции.',
  },
};

export default function InspirationPage() {
  return <LandingInspiration />;
}
