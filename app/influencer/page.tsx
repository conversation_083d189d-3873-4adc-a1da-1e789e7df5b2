import type { Metadata } from 'next';
import LandingInfluencer from '@/src/pages/microLandings/Influencers';

export const metadata: Metadata = {
  title: 'Создание виртуального инфлюэнсера с ИИ - Холст.ИИ',
  description: 'Придумайте идеального цифрового героя для соцсетей. Вы задаёте внешний вид, стиль и характер — ИИ создаёт уникального инфлюэнсера, готового вести аккаунты.',
  keywords: 'виртуальный инфлюэнсер, инстаграмм influencer, ии блогер, цифровой герой, создание персонажа, Instagram, TikTok, YouTube, VK, RuTube',
  openGraph: {
    title: 'Создание виртуального инфлюэнсера с ИИ - Холст.ИИ',
    description: 'Придумайте идеального цифрового героя для соцсетей. Вы задаёте внешний вид, стиль и характер — ИИ создаёт уникального инфлюэнсера, готового вести аккаунты.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Создание виртуального инфлюэнсера с ИИ - Холст.ИИ',
    description: 'Придумайте идеального цифрового героя для соцсетей. Вы задаёте внешний вид, стиль и характер — ИИ создаёт уникального инфлюэнсера, готового вести аккаунты.',
  },
};

export default function InfluencerPage() {
  return <LandingInfluencer />;
}
