import type { Metadata } from 'next';
import LandingMarketplace from '@/src/pages/microLandings/Marketplace';

export const metadata: Metadata = {
  title: 'Создание обложек для маркетплейсов с ИИ - Холст.ИИ',
  description: 'Загрузите фото или просто опишите товар — ИИ подготовит готовые изображения и видео, соответствующие стандартам OZON, Wildberries, Яндекс Маркет и других площадок.',
  keywords: 'обложки маркетплейс,обложки вайлдберрис,Wildberries, OZON, ЯндексМаркет,  Amazon, товарные фото, 3D рендеры, баннеры для акций',
  openGraph: {
    title: 'Создание обложек для маркетплейсов с ИИ - Холст.ИИ',
    description: 'Загрузите фото или просто опишите товар — ИИ подготовит готовые изображения и видео, соответствующие стандартам OZON, Wildberries, Яндекс Маркет и других площадок.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Создание обложек для маркетплейсов с ИИ - Холст.ИИ',
    description: 'Загрузите фото или просто опишите товар — ИИ подготовит готовые изображения и видео, соответствующие стандартам OZON, Wildberries, Яндекс Маркет и других площадок.',
  },
};

export default function MarketplacePage() {
  return <LandingMarketplace />;
}
