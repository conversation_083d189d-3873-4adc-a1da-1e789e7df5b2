import type { Metadata } from 'next';
import LandingLivingPhoto from '@/src/pages/microLandings/LivePhoto';

export const metadata: Metadata = {
  title: 'Оживление персонажей с помощью ИИ - Холст.ИИ',
  description: 'Превратите обычные изображения в динамичные и живые визуалы. ИИ добавляет движение, эмоции и атмосферу, создавая анимации, которые цепляют взгляд.',
  keywords: 'оживление фото, оживление персонажей, фото в видео, анимация изображений, живые фото, динамичные визуалы, анимация для соцсетей',
  openGraph: {
    title: 'Оживление персонажей с помощью ИИ - Холст.ИИ',
    description: 'Превратите обычные изображения в динамичные и живые визуалы. ИИ добавляет движение, эмоции и атмосферу, создавая анимации, которые цепляют взгляд.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Оживление персонажей с помощью ИИ - Холст.ИИ',
    description: 'Превратите обычные изображения в динамичные и живые визуалы. ИИ добавляет движение, эмоции и атмосферу, создавая анимации, которые цепляют взгляд.',
  },
};

export default function LivingPhotoPage() {
  return <LandingLivingPhoto />;
}
